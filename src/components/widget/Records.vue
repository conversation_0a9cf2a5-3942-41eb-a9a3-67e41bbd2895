<template>
  <GLoading :show="loading" class="records-content">
    <section v-for="item in historyBetResult" :key="item" class="mb-4">
      <div class="text-white font-medium flex align-center justify-between">
        <p>NO.{{ item.roundId }}</p>
        <p>
          {{ $t('total_win') }}：
          <span :class="returnColor(item.totalAwardAmount)">
          {{ returnTotal(item.totalAwardAmount) }}
        </span>
        </p>
      </div>
      <div class="mt-[8px]">
        <table border="0" cellpadding="0" cellspacing="0">
          <thead>
          <tr>
            <th>{{ $t('betting_option') }}</th>
            <th>{{ $t('bets') }}</th>
            <th>{{ $t('prize') }}</th>
            <th>{{ $t('win') }}</th>
          </tr>
          </thead>
          <tbody>
          <tr v-for="innerItem in item.recordLists">
            <td>
              <template v-if="innerItem.playTypeId == '1'">
                <img :src="`/images/ball-${innerItem.betLocation}.png`" alt="" />
              </template>
              <template v-else-if="innerItem.playTypeId == '2'">
                {{ innerItem.betLocation == '1' ? SpecialBet.DRAGON : SpecialBet.TIGER }}
              </template>
              <template v-else-if="innerItem.playTypeId == '3'">
                {{ innerItem.betLocation == '1' ? SpecialBet.BIG : SpecialBet.SMALL }}
              </template>
              <template v-else>
                {{ innerItem.betLocation == '1' ? SpecialBet.SINGULAR : SpecialBet.EVEN }}
              </template>
            </td>
            <td class="w-[72px] text-center">{{ innerItem.betAmount }}</td>
            <td class="w-[72px] text-center">{{ innerItem.awardAmount }}</td>
            <td :class="`w-[72px] text-center ${returnColor(innerItem.winOrLose)}`">
              {{ innerItem.winOrLose }}
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </section>
    <Empty v-if="!historyBetResult.length" :class="{ 'h-60': isHorizontal, 'h-full': isVertical }" />
  </GLoading>
</template>

<script setup lang="ts">
import {onMounted, ref} from "vue";
import {storeToRefs} from "pinia";
import { useSocketFetch } from "@/composables/useSocketFetch";
import {SpecialBet} from "@/constant/gameData.ts";
import {useGameStore, useMainStore} from "@/stores";

import GLoading from "@/components/ui/GLoading/index.vue";
import Empty from "@/components/ui/GEmpty/index.vue"

const { historyBetResult } = storeToRefs(useGameStore());
const { setHistoryBetResult } = useGameStore();
const { isHorizontal, isVertical } = storeToRefs(useMainStore());

const loading = ref<boolean>(true);

const returnColor = (data: number) => {
  if (data > 0) return 'green'
  if (data < 0) return 'red'
  return ''
}

const returnTotal = (data: number) => {
  if (data > 0) return `+${data}`
  return data ?? 0
}

const fetchData = async () => {
  loading.value = true;
  try {
    const { promise } = useSocketFetch('get-game-history-bet-req', 'get-game-history-bet-res');
    const res = await promise;
    if (res.data?.code === 200) {
      setHistoryBetResult(res.data.data);
    }
  } catch (e) {
    console.error('fetch data error', e);
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  fetchData();
});
</script>

<style>
.records-content {
  height: 100%;
  color: #fff;
  font-size: 10px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}

table {
  width: 100%;
  border: 1px solid #363538;
  border-collapse: collapse;
}

th,
thead {
  height: 30px;
  border: none;
  background-color: #363538;
  color: #ffffff;
  font-weight: 700;
}

td {
  padding: 6px 0;
  height: 30px;
  border: 1px solid #363538;
  color: #ffffff;
  font-weight: 400;
  text-align: center;
}

td.red,
p .red {
  color: #ff003f;
}

td.green,
p .green {
  color: #00c82b;
}

td img {
  width: 20px;
  height: 20px;
  margin: 0 auto;
}
</style>
