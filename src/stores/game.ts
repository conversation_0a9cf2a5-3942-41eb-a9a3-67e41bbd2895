import {computed, ref} from "vue";
import {defineStore} from "pinia";
import {GameStatus, SocketEventCode} from "@/constant/gameData.ts";

const SocketStatusToGameStatusMap: Partial<{ [key in SocketEventCode]: GameStatus }> = {
    [SocketEventCode.WAIT]: GameStatus.GAME_WAIT,
    [SocketEventCode.BET]: GameStatus.GAME_BET,
    [SocketEventCode.BET_END]: GameStatus.GAME_BET_END,
    [SocketEventCode.GAME_START]: GameStatus.GAME_START,
    [SocketEventCode.GAME_END]: GameStatus.GAME_END,
    [SocketEventCode.SETTLEMENT]: GameStatus.GAME_SETTLEMENT,
}

export const useGameStore = defineStore("game", () => {
    // 投注筹码列表
    const betChipsList = ref<number[]>([]);
    const setBetChipsList = (data: BetChipsResult) => {
        const betData = data?.betList || data?.originBetList || '0.1,0.5,1,2,5,10';
        betChipsList.value = betData.split(',').map(Number);
    }

    // 播放流列表
    const videoStreamList = ref<VideoStreamData>({
        domestic: {},  //国内
        international: {}, //国外
    });
    // 是否使用国内流
    const isUseDomesticStream = ref<boolean>(false);
    const useStreamList = computed(() => {
        return isUseDomesticStream.value ?
            videoStreamList.value.domestic :
            videoStreamList.value.international;
    });
    const videoQualityList = computed(() => {
        return Object
          .keys(useStreamList.value || {})
          .map(Number)
          .sort((a, b) => a - b);
    });
    const setVideoStreamList = (data: any) => {
        const format = (data: VideoStream[]) => {
            const result: { [key in number]: any } = {};
            data.forEach(item => {
                if (!result[item.resolution]) {
                    result[item.resolution] = {};
                }
                if (item.url.includes('a_')) {
                    result[item.resolution]['a'] = item;
                } else {
                    result[item.resolution]['b'] = item;
                }
            });
            return result;
        }
        videoStreamList.value = {
            domestic: format(data?.domestic || []),
            international: format(data?.international || []),
        };
    }
    const setIsUseDomesticStream = (isDomestic: boolean) => {
        isUseDomesticStream.value = isDomestic;
    }

    const isSocketOnline = ref(false);
    const setIsSocketOnline = (isOnline: boolean) => {
        isSocketOnline.value = isOnline;
    }

    // 服务器时间
    const serverTime = ref<number>(0);
    const setServerTime = (time: number) => {
        serverTime.value = time;
    }

    // 游戏状态
    const gameStatus = ref<GameStatus>();
    const setGameStatus = (status: GameStatus) => {
        gameStatus.value = status;
    }

    // 游戏玩法
    const gamePlayType = ref<GameTypeData[]>();
    const setGamePlayType = (list: any) => {
        gamePlayType.value = list;
    }

    // 游戏id
    const gameId = ref<string>("");
    const setGameId = (id: string) => {
        gameId.value = id;
    }

    // 期数
    const gameRoundId = ref<string>("000");
    const setGameRoundId = (id: string) => {
        gameRoundId.value = id;
    }

    // 游戏投注结束时间
    const gameBetEndTime = ref<number>();
    /**
     * 更新游戏投注结束时间（本地时间）
     * @param curr 服务器当前时间
     * @param end 服务器结束时间
     */
    const setGameBetEndTime = (curr: number, end: number) => {
        const diff = end - curr;
        if (diff > 0) {
            gameBetEndTime.value = Date.now() + diff;
        } else {
            gameBetEndTime.value = undefined;
        }
    }
    /**
     * 清除游戏投注结束时间
     */
    const cleanGameBetEndTime = () => {
        gameBetEndTime.value = undefined;
    }

    // 等待下一场游戏开始间隔时间
    const gameNextWaitEndTime = ref<number>();
    /**
     * 更新下一场游戏开始需要等待的时间
     * @param curr 服务器当前时间
     * @param end 服务器结束时间
     */
    const setGameNextWaitEndTime = (curr: number, end: number) => {
        const diff = end - curr;
        if (diff > 0) {
            gameNextWaitEndTime.value = Date.now() + diff;
        } else {
            gameNextWaitEndTime.value = undefined;
        }
    }
    /**
     * 清除下一场游戏开始需要等待的时间
     */
    const cleanGameNextWaitEndTime = () => {
        gameNextWaitEndTime.value = undefined;
    }

    // 比赛结果
    const gameResult = ref<string[]>(['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']);
    const setGameResult = (result: any) => {
        gameResult.value = (result ?? "").split(",");
    };

    // 本期获奖金额
    const gameAwardAmount = ref(0);
    const setGameAwardAmount = (amount: number) => {
        gameAwardAmount.value = amount;
    }

    // 当前使用的筹码索引
    const activeChipIndex = ref(2);
    const setActiveChipIndex = (index: number) => {
        activeChipIndex.value = index
    }
    
    // 历史球号胜率
    const historyBallRate = ref<Record<string, string>>({});
    const setHistoryBallRate = (data: Record<string, string>) => {
        historyBallRate.value = data;
    }

    // 历史趋势记录
    const historyTrend = ref<HistoryTrend[]>([]);
    const setHistoryTrend = (data: HistoryTrend[]) => {
        historyTrend.value = data;
    }

    // 历史投注结果
    const historyBetResult = ref<any[]>([]);
    const setHistoryBetResult = (data: any) => {
        historyBetResult.value = data
    }
    
    // 投注获胜信息
    const gameWinBet = ref<WinLocation>({});
    const setGameWinBet = (result: WinLocation) => {
        gameWinBet.value = result;
    }

    // 根据游戏事件相关socket返回的数据更新本地信息
    const updateGameInfo = ({ currentTimeMillis, path, data: resData }: SocketData) => {
        if (currentTimeMillis < serverTime.value) return;
        const { code, data } = resData;
        setServerTime(currentTimeMillis);
        if (code === 200) {
            if (data?.gameId) setGameId(data.gameId);
            if (data?.roundId) setGameRoundId(data.roundId);
            if (data?.betTime || data?.endBetTime) setGameBetEndTime(currentTimeMillis, data.betTime || data.endBetTime);
            if (path === SocketEventCode.SETTLEMENT) {
                setGameResult(data.result);
                setGameNextWaitEndTime(currentTimeMillis, currentTimeMillis + data.gapTimes * 1000);
                setGameWinBet({
                    type1Loc: data.type1Loc,
                    type2Loc: data.type2Loc,
                    type3Loc: data.type3Loc,
                    type4Loc: data.type4Loc,
                });
            }
            if (path === SocketEventCode.NOTICE_ONE_PRIZE) {
                setGameAwardAmount(data?.totalAwardAmount);
            }
            if (Reflect.has(SocketStatusToGameStatusMap, path)) {
                gameStatus.value = SocketStatusToGameStatusMap[path as SocketEventCode];
            }
        }
    }

    return {
        isSocketOnline, setIsSocketOnline,
        gameStatus, setGameStatus,
        gameId, setGameId,
        gameRoundId, setGameRoundId,
        gameBetEndTime, setGameBetEndTime, cleanGameBetEndTime,
        gameNextWaitEndTime, setGameNextWaitEndTime, cleanGameNextWaitEndTime,
        activeChipIndex, setActiveChipIndex,
        gameResult, setGameResult,
        gameWinBet, setGameWinBet,
        updateGameInfo,
        useStreamList, videoQualityList, setVideoStreamList,
        isUseDomesticStream, setIsUseDomesticStream,
        gameAwardAmount, setGameAwardAmount,
        betChipsList, setBetChipsList,
        gamePlayType, setGamePlayType,
        historyBetResult, setHistoryBetResult,
        historyBallRate, setHistoryBallRate,
        historyTrend, setHistoryTrend,
    };
});