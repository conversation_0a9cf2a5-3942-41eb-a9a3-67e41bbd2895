<script setup lang="ts">
import {storeToRefs} from "pinia";
import {useGameStore} from "@/stores";

import CurrentPeriod from "@/components/widget/CurrentPeriod.vue";
import DragonTigerBet from "@/components/widget/bet/DragonTigerBet.vue";
import BallBet from "@/components/widget/bet/BallBet.vue";
import BetBar from "@/components/widget/bet/BetBar.vue";

const { historyBallRate } = storeToRefs(useGameStore());
</script>

<template>
  <div class="px-1 py-3">
    <div class="px-2">
      <CurrentPeriod />
    </div>

    <div class="my-3 grid grid-cols-10 text-white">
      <div v-for="item in 10" :key="item" class="text-center text-white/90">
        <img class="block size-5 mx-auto" :src="`/images/ball-${item}.png`" :alt="`ball ${item}`" />
        <div class="mt-1.5">{{ historyBallRate[item] }}</div>
      </div>
    </div>

    <div class="flex flex-col gap-2.5">
      <DragonTigerBet />
      <BallBet />
      <BetBar />
    </div>
  </div>
</template>

<style scoped>

</style>
