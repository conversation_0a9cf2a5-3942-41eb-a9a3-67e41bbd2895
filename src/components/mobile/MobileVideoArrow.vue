<script setup lang="ts">
import {computed} from "vue";
import {storeToRefs} from "pinia";
import {useMainStore} from "@/stores";
import {CameraType} from "@/constant/gameData.ts";
import emitter from "@/utils/emitter.ts";

const { cameraInfo } = storeToRefs(useMainStore ());
const { toggleCameraType } = useMainStore();

const showRight = computed(() => cameraInfo.value.type === CameraType.A);
const onArrowClick = () => {
  emitter.emit('play-sound', 'SOUND_1');
  toggleCameraType()
}
</script>

<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="56"
    viewBox="0 0 20 56"
    fill="none"
    :class="{
      'absolute inset-y-0 m-auto z-10': true,
      'right-4 ': showRight,
      'left-4 rotate-180': !showRight,
    }"
    @click="onArrowClick"
  >
    <path d="M0 6.76049L10 1.21094L20 6.76049V48.9371L10 54.4866L0 48.9371V6.76049Z" fill="black" fill-opacity="0.5"/>
    <path d="M1.875 6.35676L10 0L18.125 6.35676L17.5 3.93514L20 6.65946V9.98919L10 2.42162L0 9.98919V6.65946L2.5 3.93514L1.875 6.35676Z" fill="url(#paint0_linear_100_1655)"/>
    <path d="M18.125 49.6432L10 56L1.875 49.6432L2.5 52.0649L6.01032e-07 49.3405L9.01547e-07 46.0108L10 53.5784L20 46.0108L20 49.3405L17.5 52.0649L18.125 49.6432Z" fill="url(#paint1_linear_100_1655)"/>
    <path d="M15.625 26.7868L5 16.3457L9.375 23.6106L11.1072 26.7868L9.375 29.6646L5 36.9295L15.625 26.7868Z" fill="white"/>
    <defs>
      <linearGradient id="paint0_linear_100_1655" x1="10" y1="0" x2="10" y2="9.68649" gradientUnits="userSpaceOnUse">
        <stop offset="0" stop-color="#FF003F"/>
        <stop offset="1" stop-color="#890022"/>
      </linearGradient>
      <linearGradient id="paint1_linear_100_1655" x1="10" y1="56" x2="10" y2="47.2216" gradientUnits="userSpaceOnUse">
        <stop offset="0" stop-color="#FF003F"/>
        <stop offset="1" stop-color="#880022"/>
      </linearGradient>
    </defs>
  </svg>
</template>

<style scoped>

</style>