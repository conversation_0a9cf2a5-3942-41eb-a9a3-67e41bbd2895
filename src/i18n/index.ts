import { createI18n } from "vue-i18n";
import en from "./en.json";
import hi from "./hi.json";
import ur from "./ur-PK.json";
import bn from "./bn.json";
import tl from "./tl.json";
import id from "./id.json";
import vi from "./vi.json";
import th from "./th.json";
import pt from "./pt.json";
import es from "./es.json";
import ja from "./ja.json";
import ko from "./ko.json";

const i18n = createI18n({
  legacy: false, // 使用 Composition API
  locale: "en", // 默认语言
  messages: {
    en,
    hi,
    ur,
    bn,
    tl,
    id,
    vi,
    th,
    pt,
    es,
    ja,
    ko,
  },
});

export const languages = [
  { label: "English", value: "en" },
  { label: "हिंदी", value: "hi" },
  { label: "پاکستان", value: "ur" },
  { label: "বাঙালি", value: "bn" },
  { label: "Pilipino", value: "tl" },
  { label: "Indonesia", value: "id" },
  { label: "Việt Nam", value: "vi" },
  { label: "แบบไทย", value: "th" },
  { label: "Português", value: "pt" },
  { label: "Español", value: "es" },
  { label: "日本語", value: "ja" },
  { label: "한국인", value: "ko" },
];

export default i18n;