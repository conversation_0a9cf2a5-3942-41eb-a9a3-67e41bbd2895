<script setup lang="ts">
import {RouterView} from "vue-router";
import {useI18n} from "vue-i18n";
import {useGameStore, useMainStore, useUserStore} from "@/stores";

const { setLang } = useMainStore();
const { setGameId, setIsUseDomesticStream } = useGameStore();
const { setToken, setMerchantId, setCurrency } = useUserStore();

const { locale } = useI18n();

const setupQueryParams = async () => {
  const search = window.location.search.replace('?', '');
  const params = search ? search.split('&') : [];
  
  const queryMap = params.reduce((curr: any, next: string) => {
    const [key, value] = next.split("=");
    curr[key] = value;
    return curr;
  }, {});

  const isInnerInter = Reflect.has(queryMap, 'innerInter');
  const lang = queryMap.lang ?? "en";
  const gameId = queryMap.gameId ?? "100201";
  const currency = queryMap.currency ?? "USD";
  const merchantId = queryMap.merchantId ?? "1";
  const token = queryMap.token;

  setLang(lang as string);
  setGameId(gameId as string);
  setToken(token as string);
  setCurrency(currency as string);
  setMerchantId(merchantId as string);
  setIsUseDomesticStream(isInnerInter);

  locale.value = lang as string;
}

// 初始化通过url传入的参数
setupQueryParams();
</script>

<template>
  <RouterView />
</template>

<style scoped></style>
