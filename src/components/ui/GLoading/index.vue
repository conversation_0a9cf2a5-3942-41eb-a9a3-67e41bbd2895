<script setup lang="ts">
import { onBeforeUnmount, ref, watch } from "vue";
import { random } from "radash";

const { show } = defineProps<{
  show: boolean;
  showNetworkSpeed?: boolean;  // 是否显示网速
}>()

const speed = ref(0);
let timer: any = 0;

const updateSpeed = () => (speed.value = random(60, 200));

watch(() => show, (val) => {
  if (val) {
    updateSpeed();
    timer = setInterval(updateSpeed, 1000);
  } else {
    window.clearInterval(timer);
    timer = null
  }
});

onBeforeUnmount(() => {
  window.clearInterval(timer);
});
</script>

<template>
  <div class="loading-wrapper relative">
    <slot />
    <div v-if="show" class="min-h-20 pb-1.5">
      <div class="loading-box absolute z-1 inset-0 size-full bg-black/50">
        <div class="size-full flex flex-col items-center justify-center gap-1.5 text-white">
          <img class="block size-15" src="/images/loading.png" alt="loading">
          <span v-if="showNetworkSpeed">{{ speed }}kb/s</span>
          <slot name="content"></slot>
          <div class="light-bar"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.loading-wrapper {
  .light-bar {
    width: 144px;
    height: 2px;
    border-radius: 1px;
    overflow: hidden;
    background-image: linear-gradient(
            to right,
            transparent 0%,
            white 40%,
            white 60%,
            transparent 100%
    );
    background-position: 50% 0;
    background-repeat: no-repeat;
    animation: run 1.2s linear infinite;
  }
}

@keyframes run {
  0% {
    background-position: -144px;
  }

  100% {
    background-position: 144px;
  }
}
</style>