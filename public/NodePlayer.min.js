var Module=typeof Module!="undefined"?Module:{};var ENVIRONMENT_IS_WEB=true;var ENVIRONMENT_IS_WORKER=false;const NP_LOGLEVEL={NONE:0,ERROR:1,INFO:2,DEBUG:3};typeof Module.logLevel>"u"&&(Module.logLevel=NP_LOGLEVEL.INFO);const logTime=()=>{let e=new Date;return e.toLocaleDateString()+" "+e.toLocaleTimeString([],{hour12:!1})},NP_ERROR=(...e)=>{Module.logLevel<NP_LOGLEVEL.ERROR||console.error(logTime(),"[ERROR]",...e)},NP_INFO=(...e)=>{Module.logLevel<NP_LOGLEVEL.INFO||console.log(logTime(),"[INFO]",...e)},NP_DEBUG=(...e)=>{Module.logLevel<NP_LOGLEVEL.DEBUG||console.log(logTime(),"[DEBUG]",...e)};Module.print=NP_INFO,Module.printErr=NP_ERROR;var moduleOverrides=Object.assign({},Module);var arguments_=[];var thisProgram="./this.program";var quit_=(status,toThrow)=>{throw toThrow};var scriptDirectory="";function locateFile(path){if(Module["locateFile"]){return Module["locateFile"](path,scriptDirectory)}return scriptDirectory+path}var read_,readAsync,readBinary;if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(typeof document!="undefined"&&document.currentScript){scriptDirectory=document.currentScript.src}if(scriptDirectory.startsWith("blob:")){scriptDirectory=""}else{scriptDirectory=scriptDirectory.substr(0,scriptDirectory.replace(/[?#].*/,"").lastIndexOf("/")+1)}{read_=url=>{var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.send(null);return xhr.responseText};if(ENVIRONMENT_IS_WORKER){readBinary=url=>{var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.responseType="arraybuffer";xhr.send(null);return new Uint8Array(xhr.response)}}readAsync=(url,onload,onerror)=>{var xhr=new XMLHttpRequest;xhr.open("GET",url,true);xhr.responseType="arraybuffer";xhr.onload=()=>{if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}onerror()};xhr.onerror=onerror;xhr.send(null)}}}else{}var out=Module["print"]||console.log.bind(console);var err=Module["printErr"]||console.error.bind(console);Object.assign(Module,moduleOverrides);moduleOverrides=null;if(Module["arguments"])arguments_=Module["arguments"];if(Module["thisProgram"])thisProgram=Module["thisProgram"];if(Module["quit"])quit_=Module["quit"];var wasmBinary;if(Module["wasmBinary"])wasmBinary=Module["wasmBinary"];var wasmMemory;var ABORT=false;var EXITSTATUS;var HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateMemoryViews(){var b=wasmMemory.buffer;Module["HEAP8"]=HEAP8=new Int8Array(b);Module["HEAP16"]=HEAP16=new Int16Array(b);Module["HEAPU8"]=HEAPU8=new Uint8Array(b);Module["HEAPU16"]=HEAPU16=new Uint16Array(b);Module["HEAP32"]=HEAP32=new Int32Array(b);Module["HEAPU32"]=HEAPU32=new Uint32Array(b);Module["HEAPF32"]=HEAPF32=new Float32Array(b);Module["HEAPF64"]=HEAPF64=new Float64Array(b)}var __ATPRERUN__=[];var __ATINIT__=[];var __ATMAIN__=[];var __ATPOSTRUN__=[];var runtimeInitialized=false;function preRun(){if(Module["preRun"]){if(typeof Module["preRun"]=="function")Module["preRun"]=[Module["preRun"]];while(Module["preRun"].length){addOnPreRun(Module["preRun"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){runtimeInitialized=true;callRuntimeCallbacks(__ATINIT__)}function preMain(){callRuntimeCallbacks(__ATMAIN__)}function postRun(){if(Module["postRun"]){if(typeof Module["postRun"]=="function")Module["postRun"]=[Module["postRun"]];while(Module["postRun"].length){addOnPostRun(Module["postRun"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnInit(cb){__ATINIT__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;function addRunDependency(id){runDependencies++;Module["monitorRunDependencies"]?.(runDependencies)}function removeRunDependency(id){runDependencies--;Module["monitorRunDependencies"]?.(runDependencies);if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}function abort(what){Module["onAbort"]?.(what);what="Aborted("+what+")";err(what);ABORT=true;EXITSTATUS=1;what+=". Build with -sASSERTIONS for more info.";var e=new WebAssembly.RuntimeError(what);throw e}var dataURIPrefix="data:application/octet-stream;base64,";var isDataURI=filename=>filename.startsWith(dataURIPrefix);function findWasmBinary(){var f="NodePlayer.min.wasm";if(!isDataURI(f)){return locateFile(f)}return f}var wasmBinaryFile;function getBinarySync(file){if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}if(readBinary){return readBinary(file)}throw"both async and sync fetching of the wasm failed"}function getBinaryPromise(binaryFile){if(!wasmBinary&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)){if(typeof fetch=="function"){return fetch(binaryFile,{credentials:"same-origin"}).then(response=>{if(!response["ok"]){throw`failed to load wasm binary file at '${binaryFile}'`}return response["arrayBuffer"]()}).catch(()=>getBinarySync(binaryFile))}}return Promise.resolve().then(()=>getBinarySync(binaryFile))}function instantiateArrayBuffer(binaryFile,imports,receiver){return getBinaryPromise(binaryFile).then(binary=>WebAssembly.instantiate(binary,imports)).then(receiver,reason=>{err(`failed to asynchronously prepare wasm: ${reason}`);abort(reason)})}function instantiateAsync(binary,binaryFile,imports,callback){if(!binary&&typeof WebAssembly.instantiateStreaming=="function"&&!isDataURI(binaryFile)&&typeof fetch=="function"){return fetch(binaryFile,{credentials:"same-origin"}).then(response=>{var result=WebAssembly.instantiateStreaming(response,imports);return result.then(callback,function(reason){err(`wasm streaming compile failed: ${reason}`);err("falling back to ArrayBuffer instantiation");return instantiateArrayBuffer(binaryFile,imports,callback)})})}return instantiateArrayBuffer(binaryFile,imports,callback)}function getWasmImports(){return{a:wasmImports}}function createWasm(){var info=getWasmImports();function receiveInstance(instance,module){wasmExports=instance.exports;wasmMemory=wasmExports["_a"];updateMemoryViews();wasmTable=wasmExports["zb"];addOnInit(wasmExports["$a"]);removeRunDependency("wasm-instantiate");return wasmExports}addRunDependency("wasm-instantiate");function receiveInstantiationResult(result){receiveInstance(result["instance"])}if(Module["instantiateWasm"]){try{return Module["instantiateWasm"](info,receiveInstance)}catch(e){err(`Module.instantiateWasm callback failed with error: ${e}`);return false}}if(!wasmBinaryFile)wasmBinaryFile=findWasmBinary();instantiateAsync(wasmBinary,wasmBinaryFile,info,receiveInstantiationResult);return{}}var tempDouble;var tempI64;var ASM_CONSTS={249070:$0=>{var npjs=NP[$0];if(npjs&&npjs.ve&&npjs.ve.getContext("webgl")&&npjs.ve.getContext("webgl").getExtension("WEBGL_lose_context")){npjs.ve.getContext("webgl").getExtension("WEBGL_lose_context").loseContext()}},249293:$0=>{var npjs=NP[$0];npjs.emit("buffer","empty")},249346:$0=>{var npjs=NP[$0];npjs.emit("start")},249389:($0,$1)=>{var npjs=NP[$0];npjs.emit("error",UTF8ToString($1))},249450:$0=>{var npjs=NP[$0];npjs.emit("buffer","buffering")},249507:$0=>{var npjs=NP[$0];npjs.emit("buffer","full")},249559:($0,$1,$2,$3)=>{var npjs=NP[$0];npjs.emit("videoSei",new Uint8Array(Module.HEAPU8.buffer,$1,$2),$3)},249655:($0,$1,$2,$3)=>{var npjs=NP[$0];npjs.emit("audioInfo",$1,$2,UTF8ToString($3))},249728:($0,$1)=>{var npjs=NP[$0];npjs.emit("videoFrame",$1)},249780:($0,$1,$2,$3)=>{var npjs=NP[$0];npjs.emit("videoInfo",$1,$2,UTF8ToString($3))},249853:$0=>{var npjs=NP[$0];npjs.emit("buffer","empty")},249906:($0,$1,$2,$3,$4,$5,$6)=>{var npjs=NP[$0];npjs.emit("stats",{buf:$1,fps:$2,abps:$3,vbps:$4,abuf:$5,ts:$6})},250025:($0,$1,$2)=>{var npjs=NP[$0];npjs.emit("click",$1,$2)}};function np_load(){window.NP={};if(NodePlayer.detectLegacyChrome()){NodePlayer.activeAudioEngine(true)}else{NodePlayer.workletAudioEngine(true)}if(typeof npAllReady=="function"){npAllReady()}window.npAllReadyFlag=true}function np_init(np){NP[np]={}}function get_dpr(){return window.devicePixelRatio||1}function get_is_mse(np){var npjs=NP[np];return npjs.isMSE}function get_is_wcs(np){var npjs=NP[np];return npjs.isWCS}function get_is_vod(np,data,size){var npjs=NP[np];npjs.emit("metadata",new Uint8Array(Module.HEAPU8.buffer,data,size).slice(0,size));return npjs.isVod}function get_hw_264(np){var npjs=NP[np];return npjs.isWCS?npjs.isWCSSupportedH264:npjs.isMSESupportedH264}function get_hw_265(np){var npjs=NP[np];return npjs.isWCS?npjs.isWCSSupportedH265:npjs.isMSESupportedH265}function ExitStatus(status){this.name="ExitStatus";this.message=`Program terminated with exit(${status})`;this.status=status}var callRuntimeCallbacks=callbacks=>{while(callbacks.length>0){callbacks.shift()(Module)}};var dynCallLegacy=(sig,ptr,args)=>{sig=sig.replace(/p/g,"i");var f=Module["dynCall_"+sig];return f(ptr,...args)};var wasmTable;var dynCall=(sig,ptr,args=[])=>{var rtn=dynCallLegacy(sig,ptr,args);return rtn};var noExitRuntime=Module["noExitRuntime"]||true;var stackRestore=val=>__emscripten_stack_restore(val);var stackSave=()=>_emscripten_stack_get_current();class ExceptionInfo{constructor(excPtr){this.excPtr=excPtr;this.ptr=excPtr-24}set_type(type){HEAPU32[this.ptr+4>>2]=type}get_type(){return HEAPU32[this.ptr+4>>2]}set_destructor(destructor){HEAPU32[this.ptr+8>>2]=destructor}get_destructor(){return HEAPU32[this.ptr+8>>2]}set_caught(caught){caught=caught?1:0;HEAP8[this.ptr+12]=caught}get_caught(){return HEAP8[this.ptr+12]!=0}set_rethrown(rethrown){rethrown=rethrown?1:0;HEAP8[this.ptr+13]=rethrown}get_rethrown(){return HEAP8[this.ptr+13]!=0}init(type,destructor){this.set_adjusted_ptr(0);this.set_type(type);this.set_destructor(destructor)}set_adjusted_ptr(adjustedPtr){HEAPU32[this.ptr+16>>2]=adjustedPtr}get_adjusted_ptr(){return HEAPU32[this.ptr+16>>2]}get_exception_ptr(){var isPointer=___cxa_is_pointer_type(this.get_type());if(isPointer){return HEAPU32[this.excPtr>>2]}var adjusted=this.get_adjusted_ptr();if(adjusted!==0)return adjusted;return this.excPtr}}var exceptionLast=0;var uncaughtExceptionCount=0;var ___cxa_throw=(ptr,type,destructor)=>{var info=new ExceptionInfo(ptr);info.init(type,destructor);exceptionLast=ptr;uncaughtExceptionCount++;throw exceptionLast};var UTF8Decoder=typeof TextDecoder!="undefined"?new TextDecoder("utf8"):undefined;var UTF8ArrayToString=(heapOrArray,idx,maxBytesToRead)=>{var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heapOrArray[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heapOrArray.buffer&&UTF8Decoder){return UTF8Decoder.decode(heapOrArray.subarray(idx,endPtr))}var str="";while(idx<endPtr){var u0=heapOrArray[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heapOrArray[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heapOrArray[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u0=(u0&7)<<18|u1<<12|u2<<6|heapOrArray[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}return str};var UTF8ToString=(ptr,maxBytesToRead)=>ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):"";var SYSCALLS={varargs:undefined,getStr(ptr){var ret=UTF8ToString(ptr);return ret}};function ___syscall_fcntl64(fd,cmd,varargs){SYSCALLS.varargs=varargs;return 0}function ___syscall_ioctl(fd,op,varargs){SYSCALLS.varargs=varargs;return 0}function ___syscall_openat(dirfd,path,flags,varargs){SYSCALLS.varargs=varargs}var __abort_js=()=>{abort("")};var __embind_register_bigint=(primitiveType,name,size,minRange,maxRange)=>{};var embind_init_charCodes=()=>{var codes=new Array(256);for(var i=0;i<256;++i){codes[i]=String.fromCharCode(i)}embind_charCodes=codes};var embind_charCodes;var readLatin1String=ptr=>{var ret="";var c=ptr;while(HEAPU8[c]){ret+=embind_charCodes[HEAPU8[c++]]}return ret};var awaitingDependencies={};var registeredTypes={};var typeDependencies={};var BindingError;var throwBindingError=message=>{throw new BindingError(message)};var InternalError;function sharedRegisterType(rawType,registeredInstance,options={}){var name=registeredInstance.name;if(!rawType){throwBindingError(`type "${name}" must have a positive integer typeid pointer`)}if(registeredTypes.hasOwnProperty(rawType)){if(options.ignoreDuplicateRegistrations){return}else{throwBindingError(`Cannot register type '${name}' twice`)}}registeredTypes[rawType]=registeredInstance;delete typeDependencies[rawType];if(awaitingDependencies.hasOwnProperty(rawType)){var callbacks=awaitingDependencies[rawType];delete awaitingDependencies[rawType];callbacks.forEach(cb=>cb())}}function registerType(rawType,registeredInstance,options={}){if(!("argPackAdvance"in registeredInstance)){throw new TypeError("registerType registeredInstance requires argPackAdvance")}return sharedRegisterType(rawType,registeredInstance,options)}var GenericWireTypeSize=8;var __embind_register_bool=(rawType,name,trueValue,falseValue)=>{name=readLatin1String(name);registerType(rawType,{name:name,fromWireType:function(wt){return!!wt},toWireType:function(destructors,o){return o?trueValue:falseValue},argPackAdvance:GenericWireTypeSize,readValueFromPointer:function(pointer){return this["fromWireType"](HEAPU8[pointer])},destructorFunction:null})};var emval_freelist=[];var emval_handles=[];var __emval_decref=handle=>{if(handle>9&&0===--emval_handles[handle+1]){emval_handles[handle]=undefined;emval_freelist.push(handle)}};var count_emval_handles=()=>emval_handles.length/2-5-emval_freelist.length;var init_emval=()=>{emval_handles.push(0,1,undefined,1,null,1,true,1,false,1);Module["count_emval_handles"]=count_emval_handles};var Emval={toValue:handle=>{if(!handle){throwBindingError("Cannot use deleted val. handle = "+handle)}return emval_handles[handle]},toHandle:value=>{switch(value){case undefined:return 2;case null:return 4;case true:return 6;case false:return 8;default:{const handle=emval_freelist.pop()||emval_handles.length;emval_handles[handle]=value;emval_handles[handle+1]=1;return handle}}}};function readPointer(pointer){return this["fromWireType"](HEAPU32[pointer>>2])}var EmValType={name:"emscripten::val",fromWireType:handle=>{var rv=Emval.toValue(handle);__emval_decref(handle);return rv},toWireType:(destructors,value)=>Emval.toHandle(value),argPackAdvance:GenericWireTypeSize,readValueFromPointer:readPointer,destructorFunction:null};var __embind_register_emval=rawType=>registerType(rawType,EmValType);var floatReadValueFromPointer=(name,width)=>{switch(width){case 4:return function(pointer){return this["fromWireType"](HEAPF32[pointer>>2])};case 8:return function(pointer){return this["fromWireType"](HEAPF64[pointer>>3])};default:throw new TypeError(`invalid float width (${width}): ${name}`)}};var __embind_register_float=(rawType,name,size)=>{name=readLatin1String(name);registerType(rawType,{name:name,fromWireType:value=>value,toWireType:(destructors,value)=>value,argPackAdvance:GenericWireTypeSize,readValueFromPointer:floatReadValueFromPointer(name,size),destructorFunction:null})};var integerReadValueFromPointer=(name,width,signed)=>{switch(width){case 1:return signed?pointer=>HEAP8[pointer]:pointer=>HEAPU8[pointer];case 2:return signed?pointer=>HEAP16[pointer>>1]:pointer=>HEAPU16[pointer>>1];case 4:return signed?pointer=>HEAP32[pointer>>2]:pointer=>HEAPU32[pointer>>2];default:throw new TypeError(`invalid integer width (${width}): ${name}`)}};var __embind_register_integer=(primitiveType,name,size,minRange,maxRange)=>{name=readLatin1String(name);if(maxRange===-1){maxRange=4294967295}var fromWireType=value=>value;if(minRange===0){var bitshift=32-8*size;fromWireType=value=>value<<bitshift>>>bitshift}var isUnsignedType=name.includes("unsigned");var checkAssertions=(value,toTypeName)=>{};var toWireType;if(isUnsignedType){toWireType=function(destructors,value){checkAssertions(value,this.name);return value>>>0}}else{toWireType=function(destructors,value){checkAssertions(value,this.name);return value}}registerType(primitiveType,{name:name,fromWireType:fromWireType,toWireType:toWireType,argPackAdvance:GenericWireTypeSize,readValueFromPointer:integerReadValueFromPointer(name,size,minRange!==0),destructorFunction:null})};var __embind_register_memory_view=(rawType,dataTypeIndex,name)=>{var typeMapping=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array];var TA=typeMapping[dataTypeIndex];function decodeMemoryView(handle){var size=HEAPU32[handle>>2];var data=HEAPU32[handle+4>>2];return new TA(HEAP8.buffer,data,size)}name=readLatin1String(name);registerType(rawType,{name:name,fromWireType:decodeMemoryView,argPackAdvance:GenericWireTypeSize,readValueFromPointer:decodeMemoryView},{ignoreDuplicateRegistrations:true})};var stringToUTF8Array=(str,heap,outIdx,maxBytesToWrite)=>{if(!(maxBytesToWrite>0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343){var u1=str.charCodeAt(++i);u=65536+((u&1023)<<10)|u1&1023}if(u<=127){if(outIdx>=endIdx)break;heap[outIdx++]=u}else if(u<=2047){if(outIdx+1>=endIdx)break;heap[outIdx++]=192|u>>6;heap[outIdx++]=128|u&63}else if(u<=65535){if(outIdx+2>=endIdx)break;heap[outIdx++]=224|u>>12;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}else{if(outIdx+3>=endIdx)break;heap[outIdx++]=240|u>>18;heap[outIdx++]=128|u>>12&63;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}}heap[outIdx]=0;return outIdx-startIdx};var stringToUTF8=(str,outPtr,maxBytesToWrite)=>stringToUTF8Array(str,HEAPU8,outPtr,maxBytesToWrite);var lengthBytesUTF8=str=>{var len=0;for(var i=0;i<str.length;++i){var c=str.charCodeAt(i);if(c<=127){len++}else if(c<=2047){len+=2}else if(c>=55296&&c<=57343){len+=4;++i}else{len+=3}}return len};var __embind_register_std_string=(rawType,name)=>{name=readLatin1String(name);var stdStringIsUTF8=name==="std::string";registerType(rawType,{name:name,fromWireType(value){var length=HEAPU32[value>>2];var payload=value+4;var str;if(stdStringIsUTF8){var decodeStartPtr=payload;for(var i=0;i<=length;++i){var currentBytePtr=payload+i;if(i==length||HEAPU8[currentBytePtr]==0){var maxRead=currentBytePtr-decodeStartPtr;var stringSegment=UTF8ToString(decodeStartPtr,maxRead);if(str===undefined){str=stringSegment}else{str+=String.fromCharCode(0);str+=stringSegment}decodeStartPtr=currentBytePtr+1}}}else{var a=new Array(length);for(var i=0;i<length;++i){a[i]=String.fromCharCode(HEAPU8[payload+i])}str=a.join("")}_free(value);return str},toWireType(destructors,value){if(value instanceof ArrayBuffer){value=new Uint8Array(value)}var length;var valueIsOfTypeString=typeof value=="string";if(!(valueIsOfTypeString||value instanceof Uint8Array||value instanceof Uint8ClampedArray||value instanceof Int8Array)){throwBindingError("Cannot pass non-string to std::string")}if(stdStringIsUTF8&&valueIsOfTypeString){length=lengthBytesUTF8(value)}else{length=value.length}var base=_malloc(4+length+1);var ptr=base+4;HEAPU32[base>>2]=length;if(stdStringIsUTF8&&valueIsOfTypeString){stringToUTF8(value,ptr,length+1)}else{if(valueIsOfTypeString){for(var i=0;i<length;++i){var charCode=value.charCodeAt(i);if(charCode>255){_free(ptr);throwBindingError("String has UTF-16 code units that do not fit in 8 bits")}HEAPU8[ptr+i]=charCode}}else{for(var i=0;i<length;++i){HEAPU8[ptr+i]=value[i]}}}if(destructors!==null){destructors.push(_free,base)}return base},argPackAdvance:GenericWireTypeSize,readValueFromPointer:readPointer,destructorFunction(ptr){_free(ptr)}})};var UTF16Decoder=typeof TextDecoder!="undefined"?new TextDecoder("utf-16le"):undefined;var UTF16ToString=(ptr,maxBytesToRead)=>{var endPtr=ptr;var idx=endPtr>>1;var maxIdx=idx+maxBytesToRead/2;while(!(idx>=maxIdx)&&HEAPU16[idx])++idx;endPtr=idx<<1;if(endPtr-ptr>32&&UTF16Decoder)return UTF16Decoder.decode(HEAPU8.subarray(ptr,endPtr));var str="";for(var i=0;!(i>=maxBytesToRead/2);++i){var codeUnit=HEAP16[ptr+i*2>>1];if(codeUnit==0)break;str+=String.fromCharCode(codeUnit)}return str};var stringToUTF16=(str,outPtr,maxBytesToWrite)=>{maxBytesToWrite??=2147483647;if(maxBytesToWrite<2)return 0;maxBytesToWrite-=2;var startPtr=outPtr;var numCharsToWrite=maxBytesToWrite<str.length*2?maxBytesToWrite/2:str.length;for(var i=0;i<numCharsToWrite;++i){var codeUnit=str.charCodeAt(i);HEAP16[outPtr>>1]=codeUnit;outPtr+=2}HEAP16[outPtr>>1]=0;return outPtr-startPtr};var lengthBytesUTF16=str=>str.length*2;var UTF32ToString=(ptr,maxBytesToRead)=>{var i=0;var str="";while(!(i>=maxBytesToRead/4)){var utf32=HEAP32[ptr+i*4>>2];if(utf32==0)break;++i;if(utf32>=65536){var ch=utf32-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}else{str+=String.fromCharCode(utf32)}}return str};var stringToUTF32=(str,outPtr,maxBytesToWrite)=>{maxBytesToWrite??=2147483647;if(maxBytesToWrite<4)return 0;var startPtr=outPtr;var endPtr=startPtr+maxBytesToWrite-4;for(var i=0;i<str.length;++i){var codeUnit=str.charCodeAt(i);if(codeUnit>=55296&&codeUnit<=57343){var trailSurrogate=str.charCodeAt(++i);codeUnit=65536+((codeUnit&1023)<<10)|trailSurrogate&1023}HEAP32[outPtr>>2]=codeUnit;outPtr+=4;if(outPtr+4>endPtr)break}HEAP32[outPtr>>2]=0;return outPtr-startPtr};var lengthBytesUTF32=str=>{var len=0;for(var i=0;i<str.length;++i){var codeUnit=str.charCodeAt(i);if(codeUnit>=55296&&codeUnit<=57343)++i;len+=4}return len};var __embind_register_std_wstring=(rawType,charSize,name)=>{name=readLatin1String(name);var decodeString,encodeString,readCharAt,lengthBytesUTF;if(charSize===2){decodeString=UTF16ToString;encodeString=stringToUTF16;lengthBytesUTF=lengthBytesUTF16;readCharAt=pointer=>HEAPU16[pointer>>1]}else if(charSize===4){decodeString=UTF32ToString;encodeString=stringToUTF32;lengthBytesUTF=lengthBytesUTF32;readCharAt=pointer=>HEAPU32[pointer>>2]}registerType(rawType,{name:name,fromWireType:value=>{var length=HEAPU32[value>>2];var str;var decodeStartPtr=value+4;for(var i=0;i<=length;++i){var currentBytePtr=value+4+i*charSize;if(i==length||readCharAt(currentBytePtr)==0){var maxReadBytes=currentBytePtr-decodeStartPtr;var stringSegment=decodeString(decodeStartPtr,maxReadBytes);if(str===undefined){str=stringSegment}else{str+=String.fromCharCode(0);str+=stringSegment}decodeStartPtr=currentBytePtr+charSize}}_free(value);return str},toWireType:(destructors,value)=>{if(!(typeof value=="string")){throwBindingError(`Cannot pass non-string to C++ string type ${name}`)}var length=lengthBytesUTF(value);var ptr=_malloc(4+length+charSize);HEAPU32[ptr>>2]=length/charSize;encodeString(value,ptr+4,length+charSize);if(destructors!==null){destructors.push(_free,ptr)}return ptr},argPackAdvance:GenericWireTypeSize,readValueFromPointer:readPointer,destructorFunction(ptr){_free(ptr)}})};var __embind_register_void=(rawType,name)=>{name=readLatin1String(name);registerType(rawType,{isVoid:true,name:name,argPackAdvance:0,fromWireType:()=>undefined,toWireType:(destructors,o)=>undefined})};var __emscripten_memcpy_js=(dest,src,num)=>HEAPU8.copyWithin(dest,src,src+num);var getTypeName=type=>{var ptr=___getTypeName(type);var rv=readLatin1String(ptr);_free(ptr);return rv};var requireRegisteredType=(rawType,humanName)=>{var impl=registeredTypes[rawType];if(undefined===impl){throwBindingError(`${humanName} has unknown type ${getTypeName(rawType)}`)}return impl};var emval_returnValue=(returnType,destructorsRef,handle)=>{var destructors=[];var result=returnType["toWireType"](destructors,handle);if(destructors.length){HEAPU32[destructorsRef>>2]=Emval.toHandle(destructors)}return result};var __emval_as=(handle,returnType,destructorsRef)=>{handle=Emval.toValue(handle);returnType=requireRegisteredType(returnType,"emval::as");return emval_returnValue(returnType,destructorsRef,handle)};var emval_symbols={};var getStringOrSymbol=address=>{var symbol=emval_symbols[address];if(symbol===undefined){return readLatin1String(address)}return symbol};var emval_methodCallers=[];var __emval_call_method=(caller,objHandle,methodName,destructorsRef,args)=>{caller=emval_methodCallers[caller];objHandle=Emval.toValue(objHandle);methodName=getStringOrSymbol(methodName);return caller(objHandle,objHandle[methodName],destructorsRef,args)};var emval_get_global=()=>{if(typeof globalThis=="object"){return globalThis}return function(){return Function}()("return this")()};var __emval_get_global=name=>{if(name===0){return Emval.toHandle(emval_get_global())}else{name=getStringOrSymbol(name);return Emval.toHandle(emval_get_global()[name])}};var emval_addMethodCaller=caller=>{var id=emval_methodCallers.length;emval_methodCallers.push(caller);return id};var emval_lookupTypes=(argCount,argTypes)=>{var a=new Array(argCount);for(var i=0;i<argCount;++i){a[i]=requireRegisteredType(HEAPU32[argTypes+i*4>>2],"parameter "+i)}return a};var createNamedFunction=(name,body)=>Object.defineProperty(body,"name",{value:name});var reflectConstruct=Reflect.construct;function newFunc(constructor,argumentList){if(!(constructor instanceof Function)){throw new TypeError(`new_ called with constructor type ${typeof constructor} which is not a function`)}var dummy=createNamedFunction(constructor.name||"unknownFunctionName",function(){});dummy.prototype=constructor.prototype;var obj=new dummy;var r=constructor.apply(obj,argumentList);return r instanceof Object?r:obj}var __emval_get_method_caller=(argCount,argTypes,kind)=>{var types=emval_lookupTypes(argCount,argTypes);var retType=types.shift();argCount--;var functionBody=`return function (obj, func, destructorsRef, args) {\n`;var offset=0;var argsList=[];if(kind===0){argsList.push("obj")}var params=["retType"];var args=[retType];for(var i=0;i<argCount;++i){argsList.push("arg"+i);params.push("argType"+i);args.push(types[i]);functionBody+=`  var arg${i} = argType${i}.readValueFromPointer(args${offset?"+"+offset:""});\n`;offset+=types[i]["argPackAdvance"]}var invoker=kind===1?"new func":"func.call";functionBody+=`  var rv = ${invoker}(${argsList.join(", ")});\n`;if(!retType.isVoid){params.push("emval_returnValue");args.push(emval_returnValue);functionBody+="  return emval_returnValue(retType, destructorsRef, rv);\n"}functionBody+="};\n";params.push(functionBody);var invokerFunction=newFunc(Function,params)(...args);var functionName=`methodCaller<(${types.map(t=>t.name).join(", ")}) => ${retType.name}>`;return emval_addMethodCaller(createNamedFunction(functionName,invokerFunction))};var __emval_get_property=(handle,key)=>{handle=Emval.toValue(handle);key=Emval.toValue(key);return Emval.toHandle(handle[key])};var __emval_new_cstring=v=>Emval.toHandle(getStringOrSymbol(v));var runDestructors=destructors=>{while(destructors.length){var ptr=destructors.pop();var del=destructors.pop();del(ptr)}};var __emval_run_destructors=handle=>{var destructors=Emval.toValue(handle);runDestructors(destructors);__emval_decref(handle)};var convertI32PairToI53Checked=(lo,hi)=>hi+2097152>>>0<4194305-!!lo?(lo>>>0)+hi*4294967296:NaN;var readEmAsmArgsArray=[];var readEmAsmArgs=(sigPtr,buf)=>{readEmAsmArgsArray.length=0;var ch;while(ch=HEAPU8[sigPtr++]){var wide=ch!=105;wide&=ch!=112;buf+=wide&&buf%8?4:0;readEmAsmArgsArray.push(ch==112?HEAPU32[buf>>2]:ch==105?HEAP32[buf>>2]:HEAPF64[buf>>3]);buf+=wide?8:4}return readEmAsmArgsArray};var runEmAsmFunction=(code,sigPtr,argbuf)=>{var args=readEmAsmArgs(sigPtr,argbuf);return ASM_CONSTS[code](...args)};var _emscripten_asm_const_int=(code,sigPtr,argbuf)=>runEmAsmFunction(code,sigPtr,argbuf);var _emscripten_set_main_loop_timing=(mode,value)=>{Browser.mainLoop.timingMode=mode;Browser.mainLoop.timingValue=value;if(!Browser.mainLoop.func){return 1}if(!Browser.mainLoop.running){Browser.mainLoop.running=true}if(mode==0){Browser.mainLoop.scheduler=function Browser_mainLoop_scheduler_setTimeout(){var timeUntilNextTick=Math.max(0,Browser.mainLoop.tickStartTime+value-_emscripten_get_now())|0;setTimeout(Browser.mainLoop.runner,timeUntilNextTick)};Browser.mainLoop.method="timeout"}else if(mode==1){Browser.mainLoop.scheduler=function Browser_mainLoop_scheduler_rAF(){Browser.requestAnimationFrame(Browser.mainLoop.runner)};Browser.mainLoop.method="rAF"}else if(mode==2){if(typeof Browser.setImmediate=="undefined"){if(typeof setImmediate=="undefined"){var setImmediates=[];var emscriptenMainLoopMessageId="setimmediate";var Browser_setImmediate_messageHandler=event=>{if(event.data===emscriptenMainLoopMessageId||event.data.target===emscriptenMainLoopMessageId){event.stopPropagation();setImmediates.shift()()}};addEventListener("message",Browser_setImmediate_messageHandler,true);Browser.setImmediate=function Browser_emulated_setImmediate(func){setImmediates.push(func);if(ENVIRONMENT_IS_WORKER){if(Module["setImmediates"]===undefined)Module["setImmediates"]=[];Module["setImmediates"].push(func);postMessage({target:emscriptenMainLoopMessageId})}else postMessage(emscriptenMainLoopMessageId,"*")}}else{Browser.setImmediate=setImmediate}}Browser.mainLoop.scheduler=function Browser_mainLoop_scheduler_setImmediate(){Browser.setImmediate(Browser.mainLoop.runner)};Browser.mainLoop.method="immediate"}return 0};var _emscripten_get_now;_emscripten_get_now=()=>performance.now();var setMainLoop=(browserIterationFunc,fps,simulateInfiniteLoop,arg,noSetTiming)=>{Browser.mainLoop.func=browserIterationFunc;Browser.mainLoop.arg=arg;var thisMainLoopId=Browser.mainLoop.currentlyRunningMainloop;function checkIsRunning(){if(thisMainLoopId<Browser.mainLoop.currentlyRunningMainloop){return false}return true}Browser.mainLoop.running=false;Browser.mainLoop.runner=function Browser_mainLoop_runner(){if(ABORT)return;if(Browser.mainLoop.queue.length>0){var start=Date.now();var blocker=Browser.mainLoop.queue.shift();blocker.func(blocker.arg);if(Browser.mainLoop.remainingBlockers){var remaining=Browser.mainLoop.remainingBlockers;var next=remaining%1==0?remaining-1:Math.floor(remaining);if(blocker.counted){Browser.mainLoop.remainingBlockers=next}else{next=next+.5;Browser.mainLoop.remainingBlockers=(8*remaining+next)/9}}Browser.mainLoop.updateStatus();if(!checkIsRunning())return;setTimeout(Browser.mainLoop.runner,0);return}if(!checkIsRunning())return;Browser.mainLoop.currentFrameNumber=Browser.mainLoop.currentFrameNumber+1|0;if(Browser.mainLoop.timingMode==1&&Browser.mainLoop.timingValue>1&&Browser.mainLoop.currentFrameNumber%Browser.mainLoop.timingValue!=0){Browser.mainLoop.scheduler();return}else if(Browser.mainLoop.timingMode==0){Browser.mainLoop.tickStartTime=_emscripten_get_now()}Browser.mainLoop.runIter(browserIterationFunc);if(!checkIsRunning())return;if(typeof SDL=="object")SDL.audio?.queueNewAudioData?.();Browser.mainLoop.scheduler()};if(!noSetTiming){if(fps&&fps>0){_emscripten_set_main_loop_timing(0,1e3/fps)}else{_emscripten_set_main_loop_timing(1,1)}Browser.mainLoop.scheduler()}if(simulateInfiniteLoop){throw"unwind"}};var handleException=e=>{if(e instanceof ExitStatus||e=="unwind"){return EXITSTATUS}quit_(1,e)};var runtimeKeepaliveCounter=0;var keepRuntimeAlive=()=>noExitRuntime||runtimeKeepaliveCounter>0;var _proc_exit=code=>{EXITSTATUS=code;if(!keepRuntimeAlive()){Module["onExit"]?.(code);ABORT=true}quit_(code,new ExitStatus(code))};var exitJS=(status,implicit)=>{EXITSTATUS=status;_proc_exit(status)};var _exit=exitJS;var maybeExit=()=>{if(!keepRuntimeAlive()){try{_exit(EXITSTATUS)}catch(e){handleException(e)}}};var callUserCallback=func=>{if(ABORT){return}try{func();maybeExit()}catch(e){handleException(e)}};var safeSetTimeout=(func,timeout)=>setTimeout(()=>{callUserCallback(func)},timeout);var warnOnce=text=>{warnOnce.shown||={};if(!warnOnce.shown[text]){warnOnce.shown[text]=1;err(text)}};var Browser={mainLoop:{running:false,scheduler:null,method:"",currentlyRunningMainloop:0,func:null,arg:0,timingMode:0,timingValue:0,currentFrameNumber:0,queue:[],pause(){Browser.mainLoop.scheduler=null;Browser.mainLoop.currentlyRunningMainloop++},resume(){Browser.mainLoop.currentlyRunningMainloop++;var timingMode=Browser.mainLoop.timingMode;var timingValue=Browser.mainLoop.timingValue;var func=Browser.mainLoop.func;Browser.mainLoop.func=null;setMainLoop(func,0,false,Browser.mainLoop.arg,true);_emscripten_set_main_loop_timing(timingMode,timingValue);Browser.mainLoop.scheduler()},updateStatus(){if(Module["setStatus"]){var message=Module["statusMessage"]||"Please wait...";var remaining=Browser.mainLoop.remainingBlockers;var expected=Browser.mainLoop.expectedBlockers;if(remaining){if(remaining<expected){Module["setStatus"](`{message} ({expected - remaining}/{expected})`)}else{Module["setStatus"](message)}}else{Module["setStatus"]("")}}},runIter(func){if(ABORT)return;if(Module["preMainLoop"]){var preRet=Module["preMainLoop"]();if(preRet===false){return}}callUserCallback(func);Module["postMainLoop"]?.()}},isFullscreen:false,pointerLock:false,moduleContextCreatedCallbacks:[],workers:[],init(){if(Browser.initted)return;Browser.initted=true;function pointerLockChange(){Browser.pointerLock=document["pointerLockElement"]===Module["canvas"]||document["mozPointerLockElement"]===Module["canvas"]||document["webkitPointerLockElement"]===Module["canvas"]||document["msPointerLockElement"]===Module["canvas"]}var canvas=Module["canvas"];if(canvas){canvas.requestPointerLock=canvas["requestPointerLock"]||canvas["mozRequestPointerLock"]||canvas["webkitRequestPointerLock"]||canvas["msRequestPointerLock"]||(()=>{});canvas.exitPointerLock=document["exitPointerLock"]||document["mozExitPointerLock"]||document["webkitExitPointerLock"]||document["msExitPointerLock"]||(()=>{});canvas.exitPointerLock=canvas.exitPointerLock.bind(document);document.addEventListener("pointerlockchange",pointerLockChange,false);document.addEventListener("mozpointerlockchange",pointerLockChange,false);document.addEventListener("webkitpointerlockchange",pointerLockChange,false);document.addEventListener("mspointerlockchange",pointerLockChange,false);if(Module["elementPointerLock"]){canvas.addEventListener("click",ev=>{if(!Browser.pointerLock&&Module["canvas"].requestPointerLock){Module["canvas"].requestPointerLock();ev.preventDefault()}},false)}}},createContext(canvas,useWebGL,setInModule,webGLContextAttributes){if(useWebGL&&Module.ctx&&canvas==Module.canvas)return Module.ctx;var ctx;var contextHandle;if(useWebGL){var contextAttributes={antialias:false,alpha:false,majorVersion:1};if(webGLContextAttributes){for(var attribute in webGLContextAttributes){contextAttributes[attribute]=webGLContextAttributes[attribute]}}if(typeof GL!="undefined"){contextHandle=GL.createContext(canvas,contextAttributes);if(contextHandle){ctx=GL.getContext(contextHandle).GLctx}}}else{ctx=canvas.getContext("2d")}if(!ctx)return null;if(setInModule){Module.ctx=ctx;if(useWebGL)GL.makeContextCurrent(contextHandle);Module.useWebGL=useWebGL;Browser.moduleContextCreatedCallbacks.forEach(callback=>callback());Browser.init()}return ctx},destroyContext(canvas,useWebGL,setInModule){},fullscreenHandlersInstalled:false,lockPointer:undefined,resizeCanvas:undefined,requestFullscreen(lockPointer,resizeCanvas){Browser.lockPointer=lockPointer;Browser.resizeCanvas=resizeCanvas;if(typeof Browser.lockPointer=="undefined")Browser.lockPointer=true;if(typeof Browser.resizeCanvas=="undefined")Browser.resizeCanvas=false;var canvas=Module["canvas"];function fullscreenChange(){Browser.isFullscreen=false;var canvasContainer=canvas.parentNode;if((document["fullscreenElement"]||document["mozFullScreenElement"]||document["msFullscreenElement"]||document["webkitFullscreenElement"]||document["webkitCurrentFullScreenElement"])===canvasContainer){canvas.exitFullscreen=Browser.exitFullscreen;if(Browser.lockPointer)canvas.requestPointerLock();Browser.isFullscreen=true;if(Browser.resizeCanvas){Browser.setFullscreenCanvasSize()}else{Browser.updateCanvasDimensions(canvas)}}else{canvasContainer.parentNode.insertBefore(canvas,canvasContainer);canvasContainer.parentNode.removeChild(canvasContainer);if(Browser.resizeCanvas){Browser.setWindowedCanvasSize()}else{Browser.updateCanvasDimensions(canvas)}}Module["onFullScreen"]?.(Browser.isFullscreen);Module["onFullscreen"]?.(Browser.isFullscreen)}if(!Browser.fullscreenHandlersInstalled){Browser.fullscreenHandlersInstalled=true;document.addEventListener("fullscreenchange",fullscreenChange,false);document.addEventListener("mozfullscreenchange",fullscreenChange,false);document.addEventListener("webkitfullscreenchange",fullscreenChange,false);document.addEventListener("MSFullscreenChange",fullscreenChange,false)}var canvasContainer=document.createElement("div");canvas.parentNode.insertBefore(canvasContainer,canvas);canvasContainer.appendChild(canvas);canvasContainer.requestFullscreen=canvasContainer["requestFullscreen"]||canvasContainer["mozRequestFullScreen"]||canvasContainer["msRequestFullscreen"]||(canvasContainer["webkitRequestFullscreen"]?()=>canvasContainer["webkitRequestFullscreen"](Element["ALLOW_KEYBOARD_INPUT"]):null)||(canvasContainer["webkitRequestFullScreen"]?()=>canvasContainer["webkitRequestFullScreen"](Element["ALLOW_KEYBOARD_INPUT"]):null);canvasContainer.requestFullscreen()},exitFullscreen(){if(!Browser.isFullscreen){return false}var CFS=document["exitFullscreen"]||document["cancelFullScreen"]||document["mozCancelFullScreen"]||document["msExitFullscreen"]||document["webkitCancelFullScreen"]||(()=>{});CFS.apply(document,[]);return true},nextRAF:0,fakeRequestAnimationFrame(func){var now=Date.now();if(Browser.nextRAF===0){Browser.nextRAF=now+1e3/60}else{while(now+2>=Browser.nextRAF){Browser.nextRAF+=1e3/60}}var delay=Math.max(Browser.nextRAF-now,0);setTimeout(func,delay)},requestAnimationFrame(func){if(typeof requestAnimationFrame=="function"){requestAnimationFrame(func);return}var RAF=Browser.fakeRequestAnimationFrame;RAF(func)},safeSetTimeout(func,timeout){return safeSetTimeout(func,timeout)},safeRequestAnimationFrame(func){return Browser.requestAnimationFrame(()=>{callUserCallback(func)})},getMimetype(name){return{jpg:"image/jpeg",jpeg:"image/jpeg",png:"image/png",bmp:"image/bmp",ogg:"audio/ogg",wav:"audio/wav",mp3:"audio/mpeg"}[name.substr(name.lastIndexOf(".")+1)]},getUserMedia(func){window.getUserMedia||=navigator["getUserMedia"]||navigator["mozGetUserMedia"];window.getUserMedia(func)},getMovementX(event){return event["movementX"]||event["mozMovementX"]||event["webkitMovementX"]||0},getMovementY(event){return event["movementY"]||event["mozMovementY"]||event["webkitMovementY"]||0},getMouseWheelDelta(event){var delta=0;switch(event.type){case"DOMMouseScroll":delta=event.detail/3;break;case"mousewheel":delta=event.wheelDelta/120;break;case"wheel":delta=event.deltaY;switch(event.deltaMode){case 0:delta/=100;break;case 1:delta/=3;break;case 2:delta*=80;break;default:throw"unrecognized mouse wheel delta mode: "+event.deltaMode}break;default:throw"unrecognized mouse wheel event: "+event.type}return delta},mouseX:0,mouseY:0,mouseMovementX:0,mouseMovementY:0,touches:{},lastTouches:{},calculateMouseCoords(pageX,pageY){var rect=Module["canvas"].getBoundingClientRect();var cw=Module["canvas"].width;var ch=Module["canvas"].height;var scrollX=typeof window.scrollX!="undefined"?window.scrollX:window.pageXOffset;var scrollY=typeof window.scrollY!="undefined"?window.scrollY:window.pageYOffset;var adjustedX=pageX-(scrollX+rect.left);var adjustedY=pageY-(scrollY+rect.top);adjustedX=adjustedX*(cw/rect.width);adjustedY=adjustedY*(ch/rect.height);return{x:adjustedX,y:adjustedY}},setMouseCoords(pageX,pageY){const{x:x,y:y}=Browser.calculateMouseCoords(pageX,pageY);Browser.mouseMovementX=x-Browser.mouseX;Browser.mouseMovementY=y-Browser.mouseY;Browser.mouseX=x;Browser.mouseY=y},calculateMouseEvent(event){if(Browser.pointerLock){if(event.type!="mousemove"&&"mozMovementX"in event){Browser.mouseMovementX=Browser.mouseMovementY=0}else{Browser.mouseMovementX=Browser.getMovementX(event);Browser.mouseMovementY=Browser.getMovementY(event)}Browser.mouseX+=Browser.mouseMovementX;Browser.mouseY+=Browser.mouseMovementY}else{if(event.type==="touchstart"||event.type==="touchend"||event.type==="touchmove"){var touch=event.touch;if(touch===undefined){return}var coords=Browser.calculateMouseCoords(touch.pageX,touch.pageY);if(event.type==="touchstart"){Browser.lastTouches[touch.identifier]=coords;Browser.touches[touch.identifier]=coords}else if(event.type==="touchend"||event.type==="touchmove"){var last=Browser.touches[touch.identifier];last||=coords;Browser.lastTouches[touch.identifier]=last;Browser.touches[touch.identifier]=coords}return}Browser.setMouseCoords(event.pageX,event.pageY)}},resizeListeners:[],updateResizeListeners(){var canvas=Module["canvas"];Browser.resizeListeners.forEach(listener=>listener(canvas.width,canvas.height))},setCanvasSize(width,height,noUpdates){var canvas=Module["canvas"];Browser.updateCanvasDimensions(canvas,width,height);if(!noUpdates)Browser.updateResizeListeners()},windowedWidth:0,windowedHeight:0,setFullscreenCanvasSize(){if(typeof SDL!="undefined"){var flags=HEAPU32[SDL.screen>>2];flags=flags|8388608;HEAP32[SDL.screen>>2]=flags}Browser.updateCanvasDimensions(Module["canvas"]);Browser.updateResizeListeners()},setWindowedCanvasSize(){if(typeof SDL!="undefined"){var flags=HEAPU32[SDL.screen>>2];flags=flags&~8388608;HEAP32[SDL.screen>>2]=flags}Browser.updateCanvasDimensions(Module["canvas"]);Browser.updateResizeListeners()},updateCanvasDimensions(canvas,wNative,hNative){if(wNative&&hNative){canvas.widthNative=wNative;canvas.heightNative=hNative}else{wNative=canvas.widthNative;hNative=canvas.heightNative}var w=wNative;var h=hNative;if(Module["forcedAspectRatio"]&&Module["forcedAspectRatio"]>0){if(w/h<Module["forcedAspectRatio"]){w=Math.round(h*Module["forcedAspectRatio"])}else{h=Math.round(w/Module["forcedAspectRatio"])}}if((document["fullscreenElement"]||document["mozFullScreenElement"]||document["msFullscreenElement"]||document["webkitFullscreenElement"]||document["webkitCurrentFullScreenElement"])===canvas.parentNode&&typeof screen!="undefined"){var factor=Math.min(screen.width/w,screen.height/h);w=Math.round(w*factor);h=Math.round(h*factor)}if(Browser.resizeCanvas){if(canvas.width!=w)canvas.width=w;if(canvas.height!=h)canvas.height=h;if(typeof canvas.style!="undefined"){canvas.style.removeProperty("width");canvas.style.removeProperty("height")}}else{if(canvas.width!=wNative)canvas.width=wNative;if(canvas.height!=hNative)canvas.height=hNative;if(typeof canvas.style!="undefined"){if(w!=wNative||h!=hNative){canvas.style.setProperty("width",w+"px","important");canvas.style.setProperty("height",h+"px","important")}else{canvas.style.removeProperty("width");canvas.style.removeProperty("height")}}}}};var _emscripten_call_worker=(id,funcName,data,size,callback,arg)=>{funcName=UTF8ToString(funcName);var info=Browser.workers[id];var callbackId=-1;if(callback){callbackId=info.callbacks.length;info.callbacks.push({func:(a1,a2,a3)=>dynCall_viii(callback,a1,a2,a3),arg:arg});info.awaited++}var transferObject={funcName:funcName,callbackId:callbackId,data:data?new Uint8Array(HEAPU8.subarray(data,data+size)):0};if(data){info.worker.postMessage(transferObject,[transferObject.data.buffer])}else{info.worker.postMessage(transferObject)}};var _emscripten_clear_interval=id=>{clearInterval(id)};var _emscripten_create_worker=url=>{url=UTF8ToString(url);var id=Browser.workers.length;var info={worker:new Worker(url),callbacks:[],awaited:0,buffer:0,bufferSize:0};info.worker.onmessage=function info_worker_onmessage(msg){if(ABORT)return;var info=Browser.workers[id];if(!info)return;var callbackId=msg.data["callbackId"];var callbackInfo=info.callbacks[callbackId];if(!callbackInfo)return;if(msg.data["finalResponse"]){info.awaited--;info.callbacks[callbackId]=null}var data=msg.data["data"];if(data){if(!data.byteLength)data=new Uint8Array(data);if(!info.buffer||info.bufferSize<data.length){if(info.buffer)_free(info.buffer);info.bufferSize=data.length;info.buffer=_malloc(data.length)}HEAPU8.set(data,info.buffer);callbackInfo.func(info.buffer,data.length,callbackInfo.arg)}else{callbackInfo.func(0,0,callbackInfo.arg)}};Browser.workers.push(info);return id};var _emscripten_date_now=()=>Date.now();var _emscripten_destroy_worker=id=>{var info=Browser.workers[id];info.worker.terminate();if(info.buffer)_free(info.buffer);Browser.workers[id]=null};var JSEvents={removeAllEventListeners(){while(JSEvents.eventHandlers.length){JSEvents._removeHandler(JSEvents.eventHandlers.length-1)}JSEvents.deferredCalls=[]},inEventHandler:0,deferredCalls:[],deferCall(targetFunction,precedence,argsList){function arraysHaveEqualContent(arrA,arrB){if(arrA.length!=arrB.length)return false;for(var i in arrA){if(arrA[i]!=arrB[i])return false}return true}for(var i in JSEvents.deferredCalls){var call=JSEvents.deferredCalls[i];if(call.targetFunction==targetFunction&&arraysHaveEqualContent(call.argsList,argsList)){return}}JSEvents.deferredCalls.push({targetFunction:targetFunction,precedence:precedence,argsList:argsList});JSEvents.deferredCalls.sort((x,y)=>x.precedence<y.precedence)},removeDeferredCalls(targetFunction){for(var i=0;i<JSEvents.deferredCalls.length;++i){if(JSEvents.deferredCalls[i].targetFunction==targetFunction){JSEvents.deferredCalls.splice(i,1);--i}}},canPerformEventHandlerRequests(){if(navigator.userActivation){return navigator.userActivation.isActive}return JSEvents.inEventHandler&&JSEvents.currentEventHandler.allowsDeferredCalls},runDeferredCalls(){if(!JSEvents.canPerformEventHandlerRequests()){return}for(var i=0;i<JSEvents.deferredCalls.length;++i){var call=JSEvents.deferredCalls[i];JSEvents.deferredCalls.splice(i,1);--i;call.targetFunction(...call.argsList)}},eventHandlers:[],removeAllHandlersOnTarget:(target,eventTypeString)=>{for(var i=0;i<JSEvents.eventHandlers.length;++i){if(JSEvents.eventHandlers[i].target==target&&(!eventTypeString||eventTypeString==JSEvents.eventHandlers[i].eventTypeString)){JSEvents._removeHandler(i--)}}},_removeHandler(i){var h=JSEvents.eventHandlers[i];h.target.removeEventListener(h.eventTypeString,h.eventListenerFunc,h.useCapture);JSEvents.eventHandlers.splice(i,1)},registerOrRemoveHandler(eventHandler){if(!eventHandler.target){return-4}if(eventHandler.callbackfunc){eventHandler.eventListenerFunc=function(event){++JSEvents.inEventHandler;JSEvents.currentEventHandler=eventHandler;JSEvents.runDeferredCalls();eventHandler.handlerFunc(event);JSEvents.runDeferredCalls();--JSEvents.inEventHandler};eventHandler.target.addEventListener(eventHandler.eventTypeString,eventHandler.eventListenerFunc,eventHandler.useCapture);JSEvents.eventHandlers.push(eventHandler)}else{for(var i=0;i<JSEvents.eventHandlers.length;++i){if(JSEvents.eventHandlers[i].target==eventHandler.target&&JSEvents.eventHandlers[i].eventTypeString==eventHandler.eventTypeString){JSEvents._removeHandler(i--)}}}return 0},getNodeNameForTarget(target){if(!target)return"";if(target==window)return"#window";if(target==screen)return"#screen";return target?.nodeName||""},fullscreenEnabled(){return document.fullscreenEnabled||document.webkitFullscreenEnabled}};var maybeCStringToJsString=cString=>cString>2?UTF8ToString(cString):cString;var specialHTMLTargets=[0,document,window];var findEventTarget=target=>{target=maybeCStringToJsString(target);var domElement=specialHTMLTargets[target]||document.querySelector(target);return domElement};var findCanvasEventTarget=findEventTarget;var _emscripten_get_canvas_element_size=(target,width,height)=>{var canvas=findCanvasEventTarget(target);if(!canvas)return-4;HEAP32[width>>2]=canvas.width;HEAP32[height>>2]=canvas.height};var getBoundingClientRect=e=>specialHTMLTargets.indexOf(e)<0?e.getBoundingClientRect():{left:0,top:0};var _emscripten_get_element_css_size=(target,width,height)=>{target=findEventTarget(target);if(!target)return-4;var rect=getBoundingClientRect(target);HEAPF64[width>>3]=rect.width;HEAPF64[height>>3]=rect.height;return 0};var getHeapMax=()=>2147483648;var growMemory=size=>{var b=wasmMemory.buffer;var pages=(size-b.byteLength+65535)/65536;try{wasmMemory.grow(pages);updateMemoryViews();return 1}catch(e){}};var _emscripten_resize_heap=requestedSize=>{var oldSize=HEAPU8.length;requestedSize>>>=0;var maxHeapSize=getHeapMax();if(requestedSize>maxHeapSize){return false}var alignUp=(x,multiple)=>x+(multiple-x%multiple)%multiple;for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignUp(Math.max(requestedSize,overGrownHeapSize),65536));var replacement=growMemory(newSize);if(replacement){return true}}return false};var _emscripten_set_canvas_element_size=(target,width,height)=>{var canvas=findCanvasEventTarget(target);if(!canvas)return-4;canvas.width=width;canvas.height=height;return 0};var fillMouseEventData=(eventStruct,e,target)=>{HEAPF64[eventStruct>>3]=e.timeStamp;var idx=eventStruct>>2;HEAP32[idx+2]=e.screenX;HEAP32[idx+3]=e.screenY;HEAP32[idx+4]=e.clientX;HEAP32[idx+5]=e.clientY;HEAP32[idx+6]=e.ctrlKey;HEAP32[idx+7]=e.shiftKey;HEAP32[idx+8]=e.altKey;HEAP32[idx+9]=e.metaKey;HEAP16[idx*2+20]=e.button;HEAP16[idx*2+21]=e.buttons;HEAP32[idx+11]=e["movementX"];HEAP32[idx+12]=e["movementY"];var rect=getBoundingClientRect(target);HEAP32[idx+13]=e.clientX-(rect.left|0);HEAP32[idx+14]=e.clientY-(rect.top|0)};var registerMouseEventCallback=(target,userData,useCapture,callbackfunc,eventTypeId,eventTypeString,targetThread)=>{if(!JSEvents.mouseEvent)JSEvents.mouseEvent=_malloc(72);target=findEventTarget(target);var mouseEventHandlerFunc=(e=event)=>{fillMouseEventData(JSEvents.mouseEvent,e,target);if(((a1,a2,a3)=>dynCall_iiii(callbackfunc,a1,a2,a3))(eventTypeId,JSEvents.mouseEvent,userData))e.preventDefault()};var eventHandler={target:target,allowsDeferredCalls:eventTypeString!="mousemove"&&eventTypeString!="mouseenter"&&eventTypeString!="mouseleave",eventTypeString:eventTypeString,callbackfunc:callbackfunc,handlerFunc:mouseEventHandlerFunc,useCapture:useCapture};return JSEvents.registerOrRemoveHandler(eventHandler)};var _emscripten_set_click_callback_on_thread=(target,userData,useCapture,callbackfunc,targetThread)=>registerMouseEventCallback(target,userData,useCapture,callbackfunc,4,"click",targetThread);var _emscripten_set_element_css_size=(target,width,height)=>{target=findEventTarget(target);if(!target)return-4;target.style.width=width+"px";target.style.height=height+"px";return 0};var _emscripten_set_interval=(cb,msecs,userData)=>setInterval(()=>{callUserCallback(()=>(a1=>dynCall_vi(cb,a1))(userData))},msecs);var _emscripten_set_mousedown_callback_on_thread=(target,userData,useCapture,callbackfunc,targetThread)=>registerMouseEventCallback(target,userData,useCapture,callbackfunc,5,"mousedown",targetThread);var registerTouchEventCallback=(target,userData,useCapture,callbackfunc,eventTypeId,eventTypeString,targetThread)=>{if(!JSEvents.touchEvent)JSEvents.touchEvent=_malloc(1696);target=findEventTarget(target);var touchEventHandlerFunc=e=>{var t,touches={},et=e.touches;for(var i=0;i<et.length;++i){t=et[i];t.isChanged=t.onTarget=0;touches[t.identifier]=t}for(var i=0;i<e.changedTouches.length;++i){t=e.changedTouches[i];t.isChanged=1;touches[t.identifier]=t}for(var i=0;i<e.targetTouches.length;++i){touches[e.targetTouches[i].identifier].onTarget=1}var touchEvent=JSEvents.touchEvent;HEAPF64[touchEvent>>3]=e.timeStamp;var idx=touchEvent>>2;HEAP32[idx+3]=e.ctrlKey;HEAP32[idx+4]=e.shiftKey;HEAP32[idx+5]=e.altKey;HEAP32[idx+6]=e.metaKey;idx+=7;var targetRect=getBoundingClientRect(target);var numTouches=0;for(var i in touches){t=touches[i];HEAP32[idx+0]=t.identifier;HEAP32[idx+1]=t.screenX;HEAP32[idx+2]=t.screenY;HEAP32[idx+3]=t.clientX;HEAP32[idx+4]=t.clientY;HEAP32[idx+5]=t.pageX;HEAP32[idx+6]=t.pageY;HEAP32[idx+7]=t.isChanged;HEAP32[idx+8]=t.onTarget;HEAP32[idx+9]=t.clientX-(targetRect.left|0);HEAP32[idx+10]=t.clientY-(targetRect.top|0);idx+=13;if(++numTouches>31){break}}HEAP32[touchEvent+8>>2]=numTouches;if(((a1,a2,a3)=>dynCall_iiii(callbackfunc,a1,a2,a3))(eventTypeId,touchEvent,userData))e.preventDefault()};var eventHandler={target:target,allowsDeferredCalls:eventTypeString=="touchstart"||eventTypeString=="touchend",eventTypeString:eventTypeString,callbackfunc:callbackfunc,handlerFunc:touchEventHandlerFunc,useCapture:useCapture};return JSEvents.registerOrRemoveHandler(eventHandler)};var _emscripten_set_touchstart_callback_on_thread=(target,userData,useCapture,callbackfunc,targetThread)=>registerTouchEventCallback(target,userData,useCapture,callbackfunc,22,"touchstart",targetThread);var webgl_enable_ANGLE_instanced_arrays=ctx=>{var ext=ctx.getExtension("ANGLE_instanced_arrays");if(ext){ctx["vertexAttribDivisor"]=(index,divisor)=>ext["vertexAttribDivisorANGLE"](index,divisor);ctx["drawArraysInstanced"]=(mode,first,count,primcount)=>ext["drawArraysInstancedANGLE"](mode,first,count,primcount);ctx["drawElementsInstanced"]=(mode,count,type,indices,primcount)=>ext["drawElementsInstancedANGLE"](mode,count,type,indices,primcount);return 1}};var webgl_enable_OES_vertex_array_object=ctx=>{var ext=ctx.getExtension("OES_vertex_array_object");if(ext){ctx["createVertexArray"]=()=>ext["createVertexArrayOES"]();ctx["deleteVertexArray"]=vao=>ext["deleteVertexArrayOES"](vao);ctx["bindVertexArray"]=vao=>ext["bindVertexArrayOES"](vao);ctx["isVertexArray"]=vao=>ext["isVertexArrayOES"](vao);return 1}};var webgl_enable_WEBGL_draw_buffers=ctx=>{var ext=ctx.getExtension("WEBGL_draw_buffers");if(ext){ctx["drawBuffers"]=(n,bufs)=>ext["drawBuffersWEBGL"](n,bufs);return 1}};var webgl_enable_WEBGL_multi_draw=ctx=>!!(ctx.multiDrawWebgl=ctx.getExtension("WEBGL_multi_draw"));var getEmscriptenSupportedExtensions=ctx=>{var supportedExtensions=["ANGLE_instanced_arrays","EXT_blend_minmax","EXT_disjoint_timer_query","EXT_frag_depth","EXT_shader_texture_lod","EXT_sRGB","OES_element_index_uint","OES_fbo_render_mipmap","OES_standard_derivatives","OES_texture_float","OES_texture_half_float","OES_texture_half_float_linear","OES_vertex_array_object","WEBGL_color_buffer_float","WEBGL_depth_texture","WEBGL_draw_buffers","EXT_color_buffer_half_float","EXT_depth_clamp","EXT_float_blend","EXT_texture_compression_bptc","EXT_texture_compression_rgtc","EXT_texture_filter_anisotropic","KHR_parallel_shader_compile","OES_texture_float_linear","WEBGL_blend_func_extended","WEBGL_compressed_texture_astc","WEBGL_compressed_texture_etc","WEBGL_compressed_texture_etc1","WEBGL_compressed_texture_s3tc","WEBGL_compressed_texture_s3tc_srgb","WEBGL_debug_renderer_info","WEBGL_debug_shaders","WEBGL_lose_context","WEBGL_multi_draw"];return(ctx.getSupportedExtensions()||[]).filter(ext=>supportedExtensions.includes(ext))};var GL={counter:1,buffers:[],programs:[],framebuffers:[],renderbuffers:[],textures:[],shaders:[],vaos:[],contexts:[],offscreenCanvases:{},queries:[],stringCache:{},unpackAlignment:4,recordError:errorCode=>{if(!GL.lastError){GL.lastError=errorCode}},getNewId:table=>{var ret=GL.counter++;for(var i=table.length;i<ret;i++){table[i]=null}return ret},genObject:(n,buffers,createFunction,objectTable)=>{for(var i=0;i<n;i++){var buffer=GLctx[createFunction]();var id=buffer&&GL.getNewId(objectTable);if(buffer){buffer.name=id;objectTable[id]=buffer}else{GL.recordError(1282)}HEAP32[buffers+i*4>>2]=id}},getSource:(shader,count,string,length)=>{var source="";for(var i=0;i<count;++i){var len=length?HEAPU32[length+i*4>>2]:undefined;source+=UTF8ToString(HEAPU32[string+i*4>>2],len)}return source},createContext:(canvas,webGLContextAttributes)=>{if(!canvas.getContextSafariWebGL2Fixed){canvas.getContextSafariWebGL2Fixed=canvas.getContext;function fixedGetContext(ver,attrs){var gl=canvas.getContextSafariWebGL2Fixed(ver,attrs);return ver=="webgl"==gl instanceof WebGLRenderingContext?gl:null}canvas.getContext=fixedGetContext}var ctx=canvas.getContext("webgl",webGLContextAttributes);if(!ctx)return 0;var handle=GL.registerContext(ctx,webGLContextAttributes);return handle},registerContext:(ctx,webGLContextAttributes)=>{var handle=GL.getNewId(GL.contexts);var context={handle:handle,attributes:webGLContextAttributes,version:webGLContextAttributes.majorVersion,GLctx:ctx};if(ctx.canvas)ctx.canvas.GLctxObject=context;GL.contexts[handle]=context;if(typeof webGLContextAttributes.enableExtensionsByDefault=="undefined"||webGLContextAttributes.enableExtensionsByDefault){GL.initExtensions(context)}return handle},makeContextCurrent:contextHandle=>{GL.currentContext=GL.contexts[contextHandle];Module.ctx=GLctx=GL.currentContext?.GLctx;return!(contextHandle&&!GLctx)},getContext:contextHandle=>GL.contexts[contextHandle],deleteContext:contextHandle=>{if(GL.currentContext===GL.contexts[contextHandle]){GL.currentContext=null}if(typeof JSEvents=="object"){JSEvents.removeAllHandlersOnTarget(GL.contexts[contextHandle].GLctx.canvas)}if(GL.contexts[contextHandle]&&GL.contexts[contextHandle].GLctx.canvas){GL.contexts[contextHandle].GLctx.canvas.GLctxObject=undefined}GL.contexts[contextHandle]=null},initExtensions:context=>{context||=GL.currentContext;if(context.initExtensionsDone)return;context.initExtensionsDone=true;var GLctx=context.GLctx;webgl_enable_ANGLE_instanced_arrays(GLctx);webgl_enable_OES_vertex_array_object(GLctx);webgl_enable_WEBGL_draw_buffers(GLctx);{GLctx.disjointTimerQueryExt=GLctx.getExtension("EXT_disjoint_timer_query")}webgl_enable_WEBGL_multi_draw(GLctx);getEmscriptenSupportedExtensions(GLctx).forEach(ext=>{if(!ext.includes("lose_context")&&!ext.includes("debug")){GLctx.getExtension(ext)}})}};var webglPowerPreferences=["default","low-power","high-performance"];var _emscripten_webgl_do_create_context=(target,attributes)=>{var a=attributes>>2;var powerPreference=HEAP32[a+(24>>2)];var contextAttributes={alpha:!!HEAP32[a+(0>>2)],depth:!!HEAP32[a+(4>>2)],stencil:!!HEAP32[a+(8>>2)],antialias:!!HEAP32[a+(12>>2)],premultipliedAlpha:!!HEAP32[a+(16>>2)],preserveDrawingBuffer:!!HEAP32[a+(20>>2)],powerPreference:webglPowerPreferences[powerPreference],failIfMajorPerformanceCaveat:!!HEAP32[a+(28>>2)],majorVersion:HEAP32[a+(32>>2)],minorVersion:HEAP32[a+(36>>2)],enableExtensionsByDefault:HEAP32[a+(40>>2)],explicitSwapControl:HEAP32[a+(44>>2)],proxyContextToMainThread:HEAP32[a+(48>>2)],renderViaOffscreenBackBuffer:HEAP32[a+(52>>2)]};var canvas=findCanvasEventTarget(target);if(!canvas){return 0}if(contextAttributes.explicitSwapControl){return 0}var contextHandle=GL.createContext(canvas,contextAttributes);return contextHandle};var _emscripten_webgl_create_context=_emscripten_webgl_do_create_context;var _emscripten_webgl_destroy_context=contextHandle=>{if(GL.currentContext==contextHandle)GL.currentContext=0;GL.deleteContext(contextHandle)};var _emscripten_webgl_make_context_current=contextHandle=>{var success=GL.makeContextCurrent(contextHandle);return success?0:-5};var ENV={};var getExecutableName=()=>thisProgram||"./this.program";var getEnvStrings=()=>{if(!getEnvStrings.strings){var lang=(typeof navigator=="object"&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8";var env={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:lang,_:getExecutableName()};for(var x in ENV){if(ENV[x]===undefined)delete env[x];else env[x]=ENV[x]}var strings=[];for(var x in env){strings.push(`${x}=${env[x]}`)}getEnvStrings.strings=strings}return getEnvStrings.strings};var stringToAscii=(str,buffer)=>{for(var i=0;i<str.length;++i){HEAP8[buffer++]=str.charCodeAt(i)}HEAP8[buffer]=0};var _environ_get=(__environ,environ_buf)=>{var bufSize=0;getEnvStrings().forEach((string,i)=>{var ptr=environ_buf+bufSize;HEAPU32[__environ+i*4>>2]=ptr;stringToAscii(string,ptr);bufSize+=string.length+1});return 0};var _environ_sizes_get=(penviron_count,penviron_buf_size)=>{var strings=getEnvStrings();HEAPU32[penviron_count>>2]=strings.length;var bufSize=0;strings.forEach(string=>bufSize+=string.length+1);HEAPU32[penviron_buf_size>>2]=bufSize;return 0};var _fd_close=fd=>52;var _fd_fdstat_get=(fd,pbuf)=>{var rightsBase=0;var rightsInheriting=0;var flags=0;{var type=2;if(fd==0){rightsBase=2}else if(fd==1||fd==2){rightsBase=64}flags=1}HEAP8[pbuf]=type;HEAP16[pbuf+2>>1]=flags;tempI64=[rightsBase>>>0,(tempDouble=rightsBase,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[pbuf+8>>2]=tempI64[0],HEAP32[pbuf+12>>2]=tempI64[1];tempI64=[rightsInheriting>>>0,(tempDouble=rightsInheriting,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[pbuf+16>>2]=tempI64[0],HEAP32[pbuf+20>>2]=tempI64[1];return 0};var _fd_read=(fd,iov,iovcnt,pnum)=>52;function _fd_seek(fd,offset_low,offset_high,whence,newOffset){var offset=convertI32PairToI53Checked(offset_low,offset_high);return 70}var printCharBuffers=[null,[],[]];var printChar=(stream,curr)=>{var buffer=printCharBuffers[stream];if(curr===0||curr===10){(stream===1?out:err)(UTF8ArrayToString(buffer,0));buffer.length=0}else{buffer.push(curr)}};var _fd_write=(fd,iov,iovcnt,pnum)=>{var num=0;for(var i=0;i<iovcnt;i++){var ptr=HEAPU32[iov>>2];var len=HEAPU32[iov+4>>2];iov+=8;for(var j=0;j<len;j++){printChar(fd,HEAPU8[ptr+j])}num+=len}HEAPU32[pnum>>2]=num;return 0};var _glActiveTexture=x0=>GLctx.activeTexture(x0);var _glAttachShader=(program,shader)=>{GLctx.attachShader(GL.programs[program],GL.shaders[shader])};var _glBindBuffer=(target,buffer)=>{GLctx.bindBuffer(target,GL.buffers[buffer])};var _glBindTexture=(target,texture)=>{GLctx.bindTexture(target,GL.textures[texture])};var _glBufferData=(target,size,data,usage)=>{GLctx.bufferData(target,data?HEAPU8.subarray(data,data+size):size,usage)};var _glClear=x0=>GLctx.clear(x0);var _glClearColor=(x0,x1,x2,x3)=>GLctx.clearColor(x0,x1,x2,x3);var _glCompileShader=shader=>{GLctx.compileShader(GL.shaders[shader])};var _glCreateProgram=()=>{var id=GL.getNewId(GL.programs);var program=GLctx.createProgram();program.name=id;program.maxUniformLength=program.maxAttributeLength=program.maxUniformBlockNameLength=0;program.uniformIdCounter=1;GL.programs[id]=program;return id};var _glCreateShader=shaderType=>{var id=GL.getNewId(GL.shaders);GL.shaders[id]=GLctx.createShader(shaderType);return id};var _glDeleteBuffers=(n,buffers)=>{for(var i=0;i<n;i++){var id=HEAP32[buffers+i*4>>2];var buffer=GL.buffers[id];if(!buffer)continue;GLctx.deleteBuffer(buffer);buffer.name=0;GL.buffers[id]=null}};var _glDeleteProgram=id=>{if(!id)return;var program=GL.programs[id];if(!program){GL.recordError(1281);return}GLctx.deleteProgram(program);program.name=0;GL.programs[id]=null};var _glDeleteShader=id=>{if(!id)return;var shader=GL.shaders[id];if(!shader){GL.recordError(1281);return}GLctx.deleteShader(shader);GL.shaders[id]=null};var _glDeleteTextures=(n,textures)=>{for(var i=0;i<n;i++){var id=HEAP32[textures+i*4>>2];var texture=GL.textures[id];if(!texture)continue;GLctx.deleteTexture(texture);texture.name=0;GL.textures[id]=null}};var _glDrawArrays=(mode,first,count)=>{GLctx.drawArrays(mode,first,count)};var _glEnableVertexAttribArray=index=>{GLctx.enableVertexAttribArray(index)};var _glGenBuffers=(n,buffers)=>{GL.genObject(n,buffers,"createBuffer",GL.buffers)};var _glGenTextures=(n,textures)=>{GL.genObject(n,textures,"createTexture",GL.textures)};var _glGetAttribLocation=(program,name)=>GLctx.getAttribLocation(GL.programs[program],UTF8ToString(name));var _glGetError=()=>{var error=GLctx.getError()||GL.lastError;GL.lastError=0;return error};var _glGetProgramInfoLog=(program,maxLength,length,infoLog)=>{var log=GLctx.getProgramInfoLog(GL.programs[program]);if(log===null)log="(unknown error)";var numBytesWrittenExclNull=maxLength>0&&infoLog?stringToUTF8(log,infoLog,maxLength):0;if(length)HEAP32[length>>2]=numBytesWrittenExclNull};var _glGetProgramiv=(program,pname,p)=>{if(!p){GL.recordError(1281);return}if(program>=GL.counter){GL.recordError(1281);return}program=GL.programs[program];if(pname==35716){var log=GLctx.getProgramInfoLog(program);if(log===null)log="(unknown error)";HEAP32[p>>2]=log.length+1}else if(pname==35719){if(!program.maxUniformLength){for(var i=0;i<GLctx.getProgramParameter(program,35718);++i){program.maxUniformLength=Math.max(program.maxUniformLength,GLctx.getActiveUniform(program,i).name.length+1)}}HEAP32[p>>2]=program.maxUniformLength}else if(pname==35722){if(!program.maxAttributeLength){for(var i=0;i<GLctx.getProgramParameter(program,35721);++i){program.maxAttributeLength=Math.max(program.maxAttributeLength,GLctx.getActiveAttrib(program,i).name.length+1)}}HEAP32[p>>2]=program.maxAttributeLength}else if(pname==35381){if(!program.maxUniformBlockNameLength){for(var i=0;i<GLctx.getProgramParameter(program,35382);++i){program.maxUniformBlockNameLength=Math.max(program.maxUniformBlockNameLength,GLctx.getActiveUniformBlockName(program,i).length+1)}}HEAP32[p>>2]=program.maxUniformBlockNameLength}else{HEAP32[p>>2]=GLctx.getProgramParameter(program,pname)}};var _glGetShaderInfoLog=(shader,maxLength,length,infoLog)=>{var log=GLctx.getShaderInfoLog(GL.shaders[shader]);if(log===null)log="(unknown error)";var numBytesWrittenExclNull=maxLength>0&&infoLog?stringToUTF8(log,infoLog,maxLength):0;if(length)HEAP32[length>>2]=numBytesWrittenExclNull};var _glGetShaderiv=(shader,pname,p)=>{if(!p){GL.recordError(1281);return}if(pname==35716){var log=GLctx.getShaderInfoLog(GL.shaders[shader]);if(log===null)log="(unknown error)";var logLength=log?log.length+1:0;HEAP32[p>>2]=logLength}else if(pname==35720){var source=GLctx.getShaderSource(GL.shaders[shader]);var sourceLength=source?source.length+1:0;HEAP32[p>>2]=sourceLength}else{HEAP32[p>>2]=GLctx.getShaderParameter(GL.shaders[shader],pname)}};var jstoi_q=str=>parseInt(str);var webglGetLeftBracePos=name=>name.slice(-1)=="]"&&name.lastIndexOf("[");var webglPrepareUniformLocationsBeforeFirstUse=program=>{var uniformLocsById=program.uniformLocsById,uniformSizeAndIdsByName=program.uniformSizeAndIdsByName,i,j;if(!uniformLocsById){program.uniformLocsById=uniformLocsById={};program.uniformArrayNamesById={};for(i=0;i<GLctx.getProgramParameter(program,35718);++i){var u=GLctx.getActiveUniform(program,i);var nm=u.name;var sz=u.size;var lb=webglGetLeftBracePos(nm);var arrayName=lb>0?nm.slice(0,lb):nm;var id=program.uniformIdCounter;program.uniformIdCounter+=sz;uniformSizeAndIdsByName[arrayName]=[sz,id];for(j=0;j<sz;++j){uniformLocsById[id]=j;program.uniformArrayNamesById[id++]=arrayName}}}};var _glGetUniformLocation=(program,name)=>{name=UTF8ToString(name);if(program=GL.programs[program]){webglPrepareUniformLocationsBeforeFirstUse(program);var uniformLocsById=program.uniformLocsById;var arrayIndex=0;var uniformBaseName=name;var leftBrace=webglGetLeftBracePos(name);if(leftBrace>0){arrayIndex=jstoi_q(name.slice(leftBrace+1))>>>0;uniformBaseName=name.slice(0,leftBrace)}var sizeAndId=program.uniformSizeAndIdsByName[uniformBaseName];if(sizeAndId&&arrayIndex<sizeAndId[0]){arrayIndex+=sizeAndId[1];if(uniformLocsById[arrayIndex]=uniformLocsById[arrayIndex]||GLctx.getUniformLocation(program,name)){return arrayIndex}}}else{GL.recordError(1281)}return-1};var _glLinkProgram=program=>{program=GL.programs[program];GLctx.linkProgram(program);program.uniformLocsById=0;program.uniformSizeAndIdsByName={}};var _glPixelStorei=(pname,param)=>{if(pname==3317){GL.unpackAlignment=param}GLctx.pixelStorei(pname,param)};var _glShaderSource=(shader,count,string,length)=>{var source=GL.getSource(shader,count,string,length);GLctx.shaderSource(GL.shaders[shader],source)};var computeUnpackAlignedImageSize=(width,height,sizePerPixel,alignment)=>{function roundedToNextMultipleOf(x,y){return x+y-1&-y}var plainRowSize=width*sizePerPixel;var alignedRowSize=roundedToNextMultipleOf(plainRowSize,alignment);return height*alignedRowSize};var colorChannelsInGlTextureFormat=format=>{var colorChannels={5:3,6:4,8:2,29502:3,29504:4};return colorChannels[format-6402]||1};var heapObjectForWebGLType=type=>{type-=5120;if(type==1)return HEAPU8;if(type==4)return HEAP32;if(type==6)return HEAPF32;if(type==5||type==28922)return HEAPU32;return HEAPU16};var toTypedArrayIndex=(pointer,heap)=>pointer>>>31-Math.clz32(heap.BYTES_PER_ELEMENT);var emscriptenWebGLGetTexPixelData=(type,format,width,height,pixels,internalFormat)=>{var heap=heapObjectForWebGLType(type);var sizePerPixel=colorChannelsInGlTextureFormat(format)*heap.BYTES_PER_ELEMENT;var bytes=computeUnpackAlignedImageSize(width,height,sizePerPixel,GL.unpackAlignment);return heap.subarray(toTypedArrayIndex(pixels,heap),toTypedArrayIndex(pixels+bytes,heap))};var _glTexImage2D=(target,level,internalFormat,width,height,border,format,type,pixels)=>{var pixelData=pixels?emscriptenWebGLGetTexPixelData(type,format,width,height,pixels,internalFormat):null;GLctx.texImage2D(target,level,internalFormat,width,height,border,format,type,pixelData)};var _glTexParameterf=(x0,x1,x2)=>GLctx.texParameterf(x0,x1,x2);var webglGetUniformLocation=location=>{var p=GLctx.currentProgram;if(p){var webglLoc=p.uniformLocsById[location];if(typeof webglLoc=="number"){p.uniformLocsById[location]=webglLoc=GLctx.getUniformLocation(p,p.uniformArrayNamesById[location]+(webglLoc>0?`[${webglLoc}]`:""))}return webglLoc}else{GL.recordError(1282)}};var _glUniform1i=(location,v0)=>{GLctx.uniform1i(webglGetUniformLocation(location),v0)};var _glUseProgram=program=>{program=GL.programs[program];GLctx.useProgram(program);GLctx.currentProgram=program};var _glVertexAttribPointer=(index,size,type,normalized,stride,ptr)=>{GLctx.vertexAttribPointer(index,size,type,!!normalized,stride,ptr)};var _glViewport=(x0,x1,x2,x3)=>GLctx.viewport(x0,x1,x2,x3);var NM_DECODER={ctxs:[null],WebGLRenderer:function(d){const o=`\n              attribute vec2 xy;\n          \n              varying highp vec2 uv;\n           \n              void main(void) {\n                gl_Position = vec4(xy, 0.0, 1.0);\n                // Map vertex coordinates (-1 to +1) to UV coordinates (0 to 1).\n                // UV coordinates are Y-flipped relative to vertex coordinates.\n                uv = vec2((1.0 + xy.x) / 2.0, (1.0 - xy.y) / 2.0);\n              }\n            `,n=`\n              varying highp vec2 uv;\n      \n              uniform sampler2D texture;\n      \n              void main(void) {\n                gl_FragColor = texture2D(texture, uv);\n              }\n            `,t=d.getContext("webgl"),e=t.createShader(t.VERTEX_SHADER);if(t.shaderSource(e,o),t.compileShader(e),!t.getShaderParameter(e,t.COMPILE_STATUS))throw t.getShaderInfoLog(e);const r=t.createShader(t.FRAGMENT_SHADER);if(t.shaderSource(r,n),t.compileShader(r),!t.getShaderParameter(r,t.COMPILE_STATUS))throw t.getShaderInfoLog(r);const a=t.createProgram();if(t.attachShader(a,e),t.attachShader(a,r),t.linkProgram(a),!t.getProgramParameter(a,t.LINK_STATUS))throw t.getProgramInfoLog(a);t.useProgram(a);const i=t.createBuffer();t.bindBuffer(t.ARRAY_BUFFER,i),t.bufferData(t.ARRAY_BUFFER,new Float32Array([-1,-1,-1,1,1,1,1,-1]),t.STATIC_DRAW);const s=t.getAttribLocation(a,"xy");t.vertexAttribPointer(s,2,t.FLOAT,!1,0,0),t.enableVertexAttribArray(s);const l=t.createTexture();return t.activeTexture(t.TEXTURE0),t.bindTexture(t.TEXTURE_2D,l),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MAG_FILTER,t.LINEAR),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MIN_FILTER,t.LINEAR),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_S,t.CLAMP_TO_EDGE),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_T,t.CLAMP_TO_EDGE),{gl:t,vertexShader:e,fragmentShader:r,shaderProgram:a,texture:l,canvas:d}},WebGLRendererDraw:function(d,o,n,t,e,r){const a=d.gl;a.texImage2D(a.TEXTURE_2D,0,a.RGBA,a.RGBA,a.UNSIGNED_BYTE,o),a.viewport(n,t,e,r),a.clearColor(0,0,0,1),a.clear(a.COLOR_BUFFER_BIT),a.drawArrays(a.TRIANGLE_FAN,0,4)},WebGLRendererFree:function(d){const o=d.gl;o.deleteTexture(d.texture),o.detachShader(d.shaderProgram,d.vertexShader),o.detachShader(d.shaderProgram,d.fragmentShader),o.deleteShader(d.vertexShader),o.deleteShader(d.fragmentShader),o.deleteProgram(d.shaderProgram)},getMediaSource:function(){if(window.ManagedMediaSource)return new window.ManagedMediaSource;if(window.MediaSource)return new window.MediaSource}};function _nodemedia_decoder_decode(d,o,n,t,e,r){let a=NM_DECODER.ctxs[d],i=a.np,s=Module.HEAPU8.subarray(o,o+n);if(i.isWCS){let l=a.decoder,u=new EncodedVideoChunk({data:s,timestamp:t,type:r===1?"key":"delta"});l.decode(u)}else if(i.isMSE){let l=t-e,u=[],h=0,c=0,w=new DataView(s.buffer,s.byteOffset,s.byteLength);for(e<a.lastDts&&(i.vi.pause(),a.mse.flush(),a.remuxer._dtsBaseInited=!1,a.lastDts=0,i.vi.currentTime=0,i.vi.play());c<n;){if(c+4>=n)return;let g=w.getUint32(c,!0),N=s.slice(c,c+4+g),E={data:N};u.push(E),h+=N.byteLength,c+=4+g}a.track.samples.push({units:u,length:h,isKeyframe:r,dts:e,cts:l,pts:t}),a.track.length+=h,a.remuxer.remux(null,a.track),a.lastDts=e;let f=0,A=0,p=i.ve.width,v=i.ve.height,_=i.vi.videoWidth,m=i.vi.videoHeight;if(_===0||m===0)return;if(a.videoWidth!=_||a.videoHeight!=m){let g=a.codec.startsWith("avc1")?"h264":"hevc";NP_INFO("video info codec="+g+" w="+_+" h="+m+" f=mse"),i.emit("videoInfo",_,m,g),a.videoWidth=_,a.videoHeight=m}if(i.scaleMode>0){let g=p/_,N=v/m;if(i.scaleMode==1){let E=Math.min(g,N);p=_*E,v=m*E,f=(i.ve.width-p)/2,A=(i.ve.height-v)/2}else{let E=Math.max(g,N);p=_*E,v=m*E,f=(i.ve.width-p)/2,A=(i.ve.height-v)/2}}NM_DECODER.WebGLRendererDraw(a.renderer,i.vi,f,A,p,v)}return i.emit("videoFrame",e),0}function _nodemedia_decoder_free(d){let o=NM_DECODER.ctxs[d],n=o.np;n.isWCS?o.decoder.close():n.isMSE&&(o.mse.destroy(),o.remuxer.destroy(),n.vi=void 0),NM_DECODER.WebGLRendererFree(o.renderer),NM_DECODER.ctxs[d]=null}function _nodemedia_decoder_init(d,o,n,t,e,r){let a=NM_DECODER.ctxs[d],i=a.np,s=UTF8ToString(t);if(a.codec=s,i.isWCS)a.decoder.configure({codec:s});else if(i.isMSE){let l={};l.id=1,l.codec=s,l.timescale=1e3,l.duration=0,l.avcc=l.hvcc=Module.HEAPU8.subarray(e,e+r),l.codecWidth=l.presentWidth=o,l.codecHeight=l.presentHeight=n,a.remuxer._onTrackMetadataReceived("video",l)}return d}function _nodemedia_decoder_new(d,o){let n=NM_DECODER.ctxs.length,t=NP[d],e={};return e.np=t,e.userdata=d,e.onevent=o,e.renderer=NM_DECODER.WebGLRenderer(t.ve),e.videoWidth=0,e.videoHeight=0,e.lastDts=0,NM_DECODER.ctxs[n]=e,t.isWCS?e.decoder=new VideoDecoder({output:function(r){let a=0,i=0,s=t.ve.width,l=t.ve.height,u=r.codedWidth,h=r.codedHeight;if(e.videoWidth!=u||e.videoHeight!=h){let c=e.codec.startsWith("avc1")?"h264":"hevc";NP_INFO("video info codec="+c+" w="+u+" h="+h+" f=wcs"),t.emit("videoInfo",u,h,c),e.videoWidth=u,e.videoHeight=h}if(t.scaleMode>0){let c=t.ve.width/r.codedWidth,w=t.ve.height/r.codedHeight;if(t.scaleMode==1){let f=Math.min(c,w);s=r.codedWidth*f,l=r.codedHeight*f,a=(t.ve.width-s)/2,i=(t.ve.height-l)/2}else{let f=Math.max(c,w);s=r.codedWidth*f,l=r.codedHeight*f,a=(t.ve.width-s)/2,i=(t.ve.height-l)/2}}NM_DECODER.WebGLRendererDraw(e.renderer,r,a,i,s,l),r.close()},error:function(r){t.emit("error",r)}}):t.isMSE&&(t.vi=document.createElement("video"),t.vi.muted=!0,t.vi.playsInline=!0,e.mse=new NodePlayer.MSE,e.mse.initialize({getCurrentTime:()=>t.vi.currentTime,getReadyState:()=>t.vi.readyState}),e.mse.isManagedMediaSource()?(t.vi.disableRemotePlayback=!0,t.vi.srcObject=e.mse.getObject()):t.vi.src=e.mse.getObjectURL(),e.mse.on("error",r=>{t.emit("error",r)}),e.mse.on("source_open",()=>{t.vi.play()}),e.mse.on("update_end",()=>{const r=t.vi.buffered,a=t.vi.currentTime;if(r.length==0)return;const i=r.end(r.length-1);i>.5&&i-a>.5&&(t.vi.currentTime=i-.2,t.vi.play())}),e.remuxer=new NodePlayer.MP4({isLive:!0}),e.remuxer.onInitSegment=(r,a)=>{e.mse.appendInitSegment(a)},e.remuxer.onMediaSegment=(r,a)=>{e.mse.appendMediaSegment(a)},e.track={type:"video",id:1,sequenceNumber:0,samples:[],length:0}),n}var NM_FETCH={loaders:[null],getFileSize:async function(d){try{const o=await fetch(d,{method:"HEAD"});if(!o.ok)throw new Error("Network response was not ok");const n=o.headers.get("Content-Length");if(!n)throw new Error("Content-Length header not found");return parseInt(n)}catch(o){throw new Error("Failed to fetch file size: "+o.message)}},downloadPartialFile:function(d,o,n){return new Promise((t,e)=>{const r=new XMLHttpRequest;r.open("GET",d),r.setRequestHeader("Range",`bytes=${o}-${n}`),r.responseType="arraybuffer",r.onload=function(){r.status>=200&&r.status<300?t(r.response):e(new Error(`Failed to download file (status ${r.status})`))},r.onerror=function(){e(new Error("Network error"))},r.send()})}};async function _nodemedia_fetch_close(d){let o=NM_FETCH.loaders[d];if(o.start){o.start=!1;try{o.reader?await o.reader.cancel():o.fetchAbortController&&o.fetchAbortController.abort(),o.socket&&(o.socket.close(),o.socket=null)}catch{}}return 0}function _nodemedia_fetch_free(d){_nodemedia_fetch_close(d);let o=NM_FETCH.loaders[d];_free(o.buf),NM_FETCH.loaders[d]=null}function _nodemedia_fetch_new(d,o){let n=NM_FETCH.loaders.length,t={};return t.fetchAbortController=new AbortController,t.userdata=d,t.onevent=o,t.start=!1,t.pause=!1,t.len=1024*1024,t.buf=_malloc(t.len),NM_FETCH.loaders[n]=t,n}async function _nodemedia_fetch_open(d,o,n){let t=UTF8ToString(o),e=NM_FETCH.loaders[d];if(e.start)return-1;if(n===0)try{let r=e.fetchAbortController.signal,a=await fetch(t,{signal:r});if(!a.ok){let i="fetch error, status code "+a.status,s=lengthBytesUTF8(i)+1,l=_malloc(s);return stringToUTF8(i,l,s+1),dynCall("viiii",e.onevent,[e.userdata,-2,l,s]),_free(l),-2}for(dynCall("viiii",e.onevent,[e.userdata,1,0,0]),e.start=!0,e.reader=a.body.getReader();e.start;){let i=await e.reader.read();if(i.done||!e.start){dynCall("viiii",e.onevent,[e.userdata,0,0,0]);break}let s=i.value.length;s>e.len&&(_free(e.buf),e.len=s*2,e.buf=_malloc(e.len)),HEAPU8.set(i.value,e.buf),dynCall("viiii",e.onevent,[e.userdata,2,e.buf,s])}}catch(r){let a=lengthBytesUTF8(r.message)+1,i=_malloc(a);stringToUTF8(r.message,i,a+1),dynCall("viiii",e.onevent,[e.userdata,-3,i,a]),_free(i)}else if(n===1)try{for(this.startByte=0,this.endByte=1e3*1024,this.contentLength=await NM_FETCH.getFileSize(t),e.start=!0;e.start||this.endByte===this.contentLength;){if(e.pause){await new Promise(s=>setTimeout(s,100));continue}let r=await NM_FETCH.downloadPartialFile(t,this.startByte,this.endByte),a=new Uint8Array(r);this.startByte=this.endByte+1,this.endByte=Math.min(this.startByte+1e3*1024,this.contentLength);let i=a.length;i>e.len&&(_free(e.buf),e.len=i*2,e.buf=_malloc(e.len)),HEAPU8.set(a,e.buf),dynCall("viiii",e.onevent,[e.userdata,2,e.buf,i])}}catch(r){let a=lengthBytesUTF8(r.message)+1,i=_malloc(a);stringToUTF8(r.message,i,a+1),dynCall("viiii",e.onevent,[e.userdata,-3,i,a]),_free(i)}else n===2&&(e.socket=new WebSocket(t),e.socket.binaryType="arraybuffer",e.start=!0,e.socket.onopen=()=>{dynCall("viiii",e.onevent,[e.userdata,1,0,0])},e.socket.onclose=()=>{dynCall("viiii",e.onevent,[e.userdata,0,0,0])},e.socket.onerror=r=>{console.log(r);let a="websocket fetch onerror -2",i=lengthBytesUTF8(a)+1,s=_malloc(i);stringToUTF8(a,s,i+1),dynCall("viiii",e.onevent,[e.userdata,-2,s,i]),_free(s)},e.socket.onmessage=r=>{try{const a=new Uint8Array(r.data);let i=a.length;i>e.len&&(_free(e.buf),e.len=i*2,e.buf=_malloc(e.len)),HEAPU8.set(a,e.buf),dynCall("viiii",e.onevent,[e.userdata,2,e.buf,i])}catch{let i="websocket fetch onerror -3",s=lengthBytesUTF8(i)+1,l=_malloc(s);stringToUTF8(i,l,s+1),dynCall("viiii",e.onevent,[e.userdata,-2,l,s]),_free(l)}});return 0}function _nodemedia_fetch_pause(d,o){let n=NM_FETCH.loaders[d];n.pause=o}var NM_SOUNDER={ctxs:[null],huffman:function(d,o){for(var n=o;d>1;d--)n+=o;return n},noop:function(){}};function _nodemedia_sounder_close(d){let o=NM_SOUNDER.ctxs[d];return o&&(o.gainNode!=null&&(o.gainNode.disconnect(),o.gainNode=void 0),o.scriptProcessorNode!=null&&(o.scriptProcessorNode.disconnect(),o.scriptProcessorNode=void 0),o.workletProcessorNode!=null&&(o.workletProcessorNode.port.postMessage({message:"stop"}),o.workletProcessorNode.disconnect(),o.workletProcessorNode=void 0),o.audioContext!=null&&(o.audioContext.close(),o.audioContext=void 0),o.timer!=null&&clearInterval(o.timer)),0}function _nodemedia_sounder_free(d){_nodemedia_sounder_close(d),_free(ctx.channelDataBuf0),_free(ctx.channelDataBuf1),NM_SOUNDER.ctxs[d]=null}function _nodemedia_sounder_new(d,o){let n=NM_SOUNDER.ctxs.length,t=NP[d],e={};return e.np=t,e.userdata=d,e.onevent=o,e.nbsamples=4096,window.workletAudioEngine?e.nbsamples=1024:window.activeAudioEngine&&(e.nbsamples=4800),e.volume=1,e.channels=2,e.channelDataBuf0=_malloc(4*e.nbsamples),e.channelDataBuf1=_malloc(4*e.nbsamples),NM_SOUNDER.ctxs[n]=e,n}function _nodemedia_sounder_open(d){const o=window.webkitAudioContext||window.AudioContext;let n=NM_SOUNDER.ctxs[d];if(n.audioContext=new o({sampleRate:48e3}),n.gainNode=n.audioContext.createGain(),window.activeAudioEngine){let t=1e3*n.nbsamples/n.audioContext.sampleRate;n.startTime=0,n.timer=setInterval(()=>{let e=n.audioContext.createBufferSource(),r=n.audioContext.createBuffer(n.channels,n.nbsamples,n.audioContext.sampleRate),a=r.getChannelData(0),i=r.getChannelData(1);dynCall("iiiii",n.onevent,[n.userdata,n.channelDataBuf0,n.channelDataBuf1,n.nbsamples])==n.nbsamples?(a.set(Module.HEAPF32.subarray(n.channelDataBuf0>>2,(n.channelDataBuf0>>2)+n.nbsamples)),i.set(Module.HEAPF32.subarray(n.channelDataBuf1>>2,(n.channelDataBuf1>>2)+n.nbsamples)),n.startTime<n.audioContext.currentTime&&(n.startTime=n.audioContext.currentTime),e.buffer=r,e.connect(n.gainNode),e.start(n.startTime),n.startTime+=r.duration):(a.fill(0),i.fill(0))},t)}else window.workletAudioEngine?n.audioContext.audioWorklet.addModule(NodePlayer.WAB()).then(()=>{let t=new Float32Array(n.nbsamples),e=new Float32Array(n.nbsamples);n.workletProcessorNode=new AudioWorkletNode(n.audioContext,"nm-wa-processor",{outputChannelCount:[n.channels]}),n.workletProcessorNode.connect(n.gainNode),n.workletProcessorNode.port.postMessage({message:"init",bufferSize:n.nbsamples}),n.workletProcessorNode.port.onmessage=r=>{n.workletProcessorNode&&(dynCall("iiiii",n.onevent,[n.userdata,n.channelDataBuf0,n.channelDataBuf1,n.nbsamples])==n.nbsamples?(t.set(Module.HEAPF32.subarray(n.channelDataBuf0>>2,(n.channelDataBuf0>>2)+n.nbsamples)),e.set(Module.HEAPF32.subarray(n.channelDataBuf1>>2,(n.channelDataBuf1>>2)+n.nbsamples)),n.workletProcessorNode.port.postMessage({message:"data",buffer:[t,e]})):n.workletProcessorNode.port.postMessage({message:"zero"}))}}):(n.scriptProcessorNode=n.audioContext.createScriptProcessor(n.nbsamples,0,n.channels),n.scriptProcessorNode.onaudioprocess=t=>{if(n.audioContext){let e=dynCall("iiiii",n.onevent,[n.userdata,n.channelDataBuf0,n.channelDataBuf1,n.nbsamples]),r=t.outputBuffer.getChannelData(0),a=t.outputBuffer.getChannelData(1);e==n.nbsamples?(r.set(Module.HEAPF32.subarray(n.channelDataBuf0>>2,(n.channelDataBuf0>>2)+n.nbsamples)),a.set(Module.HEAPF32.subarray(n.channelDataBuf1>>2,(n.channelDataBuf1>>2)+n.nbsamples))):(r.fill(0),a.fill(0))}},n.scriptProcessorNode.connect(n.gainNode));return n.gainNode.connect(n.audioContext.destination),n.audioContext.resume(),n.audioContext.sampleRate}var _nodemedia_sounder_resume=function(d){let o=NM_SOUNDER.ctxs[d];o&&o.audioContext&&o.audioContext.resume().then(()=>{o.np.emit("resume successed ",o.audioContext.state==="running",o.audioContext.state)});const n="data:audio/mpeg;base64,//uQx"+NM_SOUNDER.huffman(23,"A")+"WGluZwAAAA8AAAACAAACcQCA"+NM_SOUNDER.huffman(16,"gICA")+NM_SOUNDER.huffman(66,"/")+"8AAABhTEFNRTMuMTAwA8MAAAAAAAAAABQgJAUHQQAB9AAAAnGMHkkI"+NM_SOUNDER.huffman(320,"A")+"//sQxAADgnABGiAAQBCqgCRMAAgEAH"+NM_SOUNDER.huffman(15,"/")+"7+n/9FTuQsQH//////2NG0jWUGlio5gLQTOtIoeR2WX////X4s9Atb/JRVCbBUpeRUq"+NM_SOUNDER.huffman(18,"/")+"9RUi0f2jn/+xDECgPCjAEQAABN4AAANIAAAAQVTEFNRTMuMTAw"+NM_SOUNDER.huffman(97,"V")+"Q==",t=navigator.userAgent.toLowerCase();if(t.indexOf("iphone")>=0&&t.indexOf("like iphone")<0||t.indexOf("ipad")>=0&&t.indexOf("like ipad")<0||t.indexOf("ipod")>=0&&t.indexOf("like ipod")<0||t.indexOf("mac os x")>=0&&navigator.maxTouchPoints>0){let r=document.createElement("div");r.innerHTML="<audio x-webkit-airplay='deny'></audio>";let a=r.children.item(0);if(a.controls=!1,a.disableRemotePlayback=!0,a.preload="auto",a.src=n,a.loop=!0,a.load(),a.paused){let i=a.play();i&&i.then(NM_SOUNDER.noop,NM_SOUNDER.noop).catch(NM_SOUNDER.noop)}}return 0};function _nodemedia_sounder_setVolume(d,o){let n=NM_SOUNDER.ctxs[d];return n&&n.gainNode&&(n.gainNode.gain.value=o),0}function _nodemedia_sounder_state(d){let o=NM_SOUNDER.ctxs[d];if(o&&o.audioContext)switch(o.audioContext.state){case"closed":return 0;case"running":return 1;case"suspended":return 2;default:break}return 0}var getCFunc=ident=>{var func=Module["_"+ident];return func};var writeArrayToMemory=(array,buffer)=>{HEAP8.set(array,buffer)};var stackAlloc=sz=>__emscripten_stack_alloc(sz);var stringToUTF8OnStack=str=>{var size=lengthBytesUTF8(str)+1;var ret=stackAlloc(size);stringToUTF8(str,ret,size);return ret};var ccall=(ident,returnType,argTypes,args,opts)=>{var toC={string:str=>{var ret=0;if(str!==null&&str!==undefined&&str!==0){ret=stringToUTF8OnStack(str)}return ret},array:arr=>{var ret=stackAlloc(arr.length);writeArrayToMemory(arr,ret);return ret}};function convertReturnValue(ret){if(returnType==="string"){return UTF8ToString(ret)}if(returnType==="boolean")return Boolean(ret);return ret}var func=getCFunc(ident);var cArgs=[];var stack=0;if(args){for(var i=0;i<args.length;i++){var converter=toC[argTypes[i]];if(converter){if(stack===0)stack=stackSave();cArgs[i]=converter(args[i])}else{cArgs[i]=args[i]}}}var ret=func(...cArgs);function onDone(ret){if(stack!==0)stackRestore(stack);return convertReturnValue(ret)}ret=onDone(ret);return ret};var cwrap=(ident,returnType,argTypes,opts)=>{var numericArgs=!argTypes||argTypes.every(type=>type==="number"||type==="boolean");var numericRet=returnType!=="string";if(numericRet&&numericArgs&&!opts){return getCFunc(ident)}return(...args)=>ccall(ident,returnType,argTypes,args,opts)};embind_init_charCodes();BindingError=Module["BindingError"]=class BindingError extends Error{constructor(message){super(message);this.name="BindingError"}};InternalError=Module["InternalError"]=class InternalError extends Error{constructor(message){super(message);this.name="InternalError"}};init_emval();Module["requestFullscreen"]=Browser.requestFullscreen;Module["requestAnimationFrame"]=Browser.requestAnimationFrame;Module["setCanvasSize"]=Browser.setCanvasSize;Module["pauseMainLoop"]=Browser.mainLoop.pause;Module["resumeMainLoop"]=Browser.mainLoop.resume;Module["getUserMedia"]=Browser.getUserMedia;Module["createContext"]=Browser.createContext;var GLctx;var wasmImports={ga:___cxa_throw,n:___syscall_fcntl64,da:___syscall_ioctl,X:___syscall_openat,aa:__abort_js,Va:__embind_register_bigint,U:__embind_register_bool,Xa:__embind_register_emval,x:__embind_register_float,c:__embind_register_integer,a:__embind_register_memory_view,T:__embind_register_std_string,v:__embind_register_std_wstring,V:__embind_register_void,$:__emscripten_memcpy_js,ha:__emval_as,fa:__emval_call_method,d:__emval_decref,f:__emval_get_global,ea:__emval_get_method_caller,ia:__emval_get_property,o:__emval_new_cstring,B:__emval_run_destructors,b:_emscripten_asm_const_int,l:_emscripten_call_worker,Q:_emscripten_clear_interval,Ia:_emscripten_create_worker,m:_emscripten_date_now,Sa:_emscripten_destroy_worker,t:_emscripten_get_canvas_element_size,P:_emscripten_get_element_css_size,W:_emscripten_resize_heap,k:_emscripten_set_canvas_element_size,Ma:_emscripten_set_click_callback_on_thread,Ka:_emscripten_set_element_css_size,S:_emscripten_set_interval,Oa:_emscripten_set_mousedown_callback_on_thread,Na:_emscripten_set_touchstart_callback_on_thread,Ga:_emscripten_webgl_create_context,u:_emscripten_webgl_destroy_context,e:_emscripten_webgl_make_context_current,Z:_environ_get,_:_environ_sizes_get,ca:_fd_close,Y:_fd_fdstat_get,ba:_fd_read,Wa:_fd_seek,z:_fd_write,La:get_dpr,ua:get_hw_264,na:get_hw_265,Qa:get_is_mse,Ba:get_is_vod,Ra:get_is_wcs,pa:_glActiveTexture,M:_glAttachShader,j:_glBindBuffer,h:_glBindTexture,i:_glBufferData,D:_glClear,E:_glClearColor,Ca:_glCompileShader,za:_glCreateProgram,Ea:_glCreateShader,ta:_glDeleteBuffers,K:_glDeleteProgram,s:_glDeleteShader,va:_glDeleteTextures,qa:_glDrawArrays,F:_glEnableVertexAttribArray,wa:_glGenBuffers,sa:_glGenTextures,I:_glGetAttribLocation,Fa:_glGetError,xa:_glGetProgramInfoLog,L:_glGetProgramiv,Aa:_glGetShaderInfoLog,N:_glGetShaderiv,r:_glGetUniformLocation,ya:_glLinkProgram,ra:_glPixelStorei,Da:_glShaderSource,p:_glTexImage2D,g:_glTexParameterf,q:_glUniform1i,J:_glUseProgram,G:_glVertexAttribPointer,C:_glViewport,R:_nodemedia_decoder_decode,w:_nodemedia_decoder_free,y:_nodemedia_decoder_init,A:_nodemedia_decoder_new,oa:_nodemedia_fetch_close,ma:_nodemedia_fetch_free,la:_nodemedia_fetch_new,ka:_nodemedia_fetch_open,ja:_nodemedia_fetch_pause,Ua:_nodemedia_sounder_close,Ta:_nodemedia_sounder_free,Ya:_nodemedia_sounder_new,Pa:_nodemedia_sounder_open,O:_nodemedia_sounder_resume,Ja:_nodemedia_sounder_setVolume,H:_nodemedia_sounder_state,Za:np_init,Ha:np_load};var wasmExports=createWasm();var ___wasm_call_ctors=()=>(___wasm_call_ctors=wasmExports["$a"])();var _npg=Module["_npg"]=()=>(_npg=Module["_npg"]=wasmExports["ab"])();var _npc=Module["_npc"]=()=>(_npc=Module["_npc"]=wasmExports["bb"])();var _npd=Module["_npd"]=(a0,a1)=>(_npd=Module["_npd"]=wasmExports["cb"])(a0,a1);var _free=Module["_free"]=a0=>(_free=Module["_free"]=wasmExports["db"])(a0);var _nps=Module["_nps"]=(a0,a1,a2)=>(_nps=Module["_nps"]=wasmExports["eb"])(a0,a1,a2);var _npt=Module["_npt"]=a0=>(_npt=Module["_npt"]=wasmExports["fb"])(a0);var _npu=Module["_npu"]=(a0,a1)=>(_npu=Module["_npu"]=wasmExports["gb"])(a0,a1);var _npv=Module["_npv"]=(a0,a1,a2)=>(_npv=Module["_npv"]=wasmExports["hb"])(a0,a1,a2);var _nor=Module["_nor"]=(a0,a1,a2)=>(_nor=Module["_nor"]=wasmExports["ib"])(a0,a1,a2);var _npr=Module["_npr"]=(a0,a1,a2,a3)=>(_npr=Module["_npr"]=wasmExports["jb"])(a0,a1,a2,a3);var _npm=Module["_npm"]=(a0,a1)=>(_npm=Module["_npm"]=wasmExports["kb"])(a0,a1);var _nsl=Module["_nsl"]=(a0,a1,a2,a3)=>(_nsl=Module["_nsl"]=wasmExports["lb"])(a0,a1,a2,a3);var _ncl=Module["_ncl"]=(a0,a1,a2,a3)=>(_ncl=Module["_ncl"]=wasmExports["mb"])(a0,a1,a2,a3);var _npb=Module["_npb"]=(a0,a1)=>(_npb=Module["_npb"]=wasmExports["nb"])(a0,a1);var _npk=Module["_npk"]=(a0,a1)=>(_npk=Module["_npk"]=wasmExports["ob"])(a0,a1);var _nsf=Module["_nsf"]=a0=>(_nsf=Module["_nsf"]=wasmExports["pb"])(a0);var _npe=Module["_npe"]=(a0,a1)=>(_npe=Module["_npe"]=wasmExports["qb"])(a0,a1);var _npy=Module["_npy"]=(a0,a1)=>(_npy=Module["_npy"]=wasmExports["rb"])(a0,a1);var _nar=Module["_nar"]=a0=>(_nar=Module["_nar"]=wasmExports["sb"])(a0);var _nas=Module["_nas"]=a0=>(_nas=Module["_nas"]=wasmExports["tb"])(a0);var _ncv=Module["_ncv"]=a0=>(_ncv=Module["_ncv"]=wasmExports["ub"])(a0);var _nuw=Module["_nuw"]=(a0,a1)=>(_nuw=Module["_nuw"]=wasmExports["vb"])(a0,a1);var _nea=Module["_nea"]=(a0,a1)=>(_nea=Module["_nea"]=wasmExports["wb"])(a0,a1);var _nev=Module["_nev"]=(a0,a1)=>(_nev=Module["_nev"]=wasmExports["xb"])(a0,a1);var _main=Module["_main"]=(a0,a1)=>(_main=Module["_main"]=wasmExports["yb"])(a0,a1);var _malloc=Module["_malloc"]=a0=>(_malloc=Module["_malloc"]=wasmExports["Ab"])(a0);var ___getTypeName=a0=>(___getTypeName=wasmExports["Bb"])(a0);var __emscripten_tempret_set=a0=>(__emscripten_tempret_set=wasmExports["Cb"])(a0);var __emscripten_stack_restore=a0=>(__emscripten_stack_restore=wasmExports["Db"])(a0);var __emscripten_stack_alloc=a0=>(__emscripten_stack_alloc=wasmExports["Eb"])(a0);var _emscripten_stack_get_current=()=>(_emscripten_stack_get_current=wasmExports["Fb"])();var ___cxa_is_pointer_type=a0=>(___cxa_is_pointer_type=wasmExports["Gb"])(a0);var dynCall_vi=Module["dynCall_vi"]=(a0,a1)=>(dynCall_vi=Module["dynCall_vi"]=wasmExports["Hb"])(a0,a1);var dynCall_viii=Module["dynCall_viii"]=(a0,a1,a2,a3)=>(dynCall_viii=Module["dynCall_viii"]=wasmExports["Ib"])(a0,a1,a2,a3);var dynCall_iiii=Module["dynCall_iiii"]=(a0,a1,a2,a3)=>(dynCall_iiii=Module["dynCall_iiii"]=wasmExports["Jb"])(a0,a1,a2,a3);var dynCall_iiiiiiii=Module["dynCall_iiiiiiii"]=(a0,a1,a2,a3,a4,a5,a6,a7)=>(dynCall_iiiiiiii=Module["dynCall_iiiiiiii"]=wasmExports["Kb"])(a0,a1,a2,a3,a4,a5,a6,a7);var dynCall_iiiii=Module["dynCall_iiiii"]=(a0,a1,a2,a3,a4)=>(dynCall_iiiii=Module["dynCall_iiiii"]=wasmExports["Lb"])(a0,a1,a2,a3,a4);var dynCall_viiii=Module["dynCall_viiii"]=(a0,a1,a2,a3,a4)=>(dynCall_viiii=Module["dynCall_viiii"]=wasmExports["Mb"])(a0,a1,a2,a3,a4);var dynCall_ii=Module["dynCall_ii"]=(a0,a1)=>(dynCall_ii=Module["dynCall_ii"]=wasmExports["Nb"])(a0,a1);var dynCall_viiiii=Module["dynCall_viiiii"]=(a0,a1,a2,a3,a4,a5)=>(dynCall_viiiii=Module["dynCall_viiiii"]=wasmExports["Ob"])(a0,a1,a2,a3,a4,a5);var dynCall_viiiiiifi=Module["dynCall_viiiiiifi"]=(a0,a1,a2,a3,a4,a5,a6,a7,a8)=>(dynCall_viiiiiifi=Module["dynCall_viiiiiifi"]=wasmExports["Pb"])(a0,a1,a2,a3,a4,a5,a6,a7,a8);var dynCall_iiiiiii=Module["dynCall_iiiiiii"]=(a0,a1,a2,a3,a4,a5,a6)=>(dynCall_iiiiiii=Module["dynCall_iiiiiii"]=wasmExports["Qb"])(a0,a1,a2,a3,a4,a5,a6);var dynCall_iiiiii=Module["dynCall_iiiiii"]=(a0,a1,a2,a3,a4,a5)=>(dynCall_iiiiii=Module["dynCall_iiiiii"]=wasmExports["Rb"])(a0,a1,a2,a3,a4,a5);var dynCall_vii=Module["dynCall_vii"]=(a0,a1,a2)=>(dynCall_vii=Module["dynCall_vii"]=wasmExports["Sb"])(a0,a1,a2);var dynCall_iii=Module["dynCall_iii"]=(a0,a1,a2)=>(dynCall_iii=Module["dynCall_iii"]=wasmExports["Tb"])(a0,a1,a2);var dynCall_viiiiii=Module["dynCall_viiiiii"]=(a0,a1,a2,a3,a4,a5,a6)=>(dynCall_viiiiii=Module["dynCall_viiiiii"]=wasmExports["Ub"])(a0,a1,a2,a3,a4,a5,a6);var dynCall_viiiiiiiii=Module["dynCall_viiiiiiiii"]=(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9)=>(dynCall_viiiiiiiii=Module["dynCall_viiiiiiiii"]=wasmExports["Vb"])(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9);var dynCall_viiiiiiii=Module["dynCall_viiiiiiii"]=(a0,a1,a2,a3,a4,a5,a6,a7,a8)=>(dynCall_viiiiiiii=Module["dynCall_viiiiiiii"]=wasmExports["Wb"])(a0,a1,a2,a3,a4,a5,a6,a7,a8);var dynCall_viiiiiii=Module["dynCall_viiiiiii"]=(a0,a1,a2,a3,a4,a5,a6,a7)=>(dynCall_viiiiiii=Module["dynCall_viiiiiii"]=wasmExports["Xb"])(a0,a1,a2,a3,a4,a5,a6,a7);var dynCall_viiiiiiiiiiii=Module["dynCall_viiiiiiiiiiii"]=(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12)=>(dynCall_viiiiiiiiiiii=Module["dynCall_viiiiiiiiiiii"]=wasmExports["Yb"])(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12);var dynCall_viiiiiiiiiiiiii=Module["dynCall_viiiiiiiiiiiiii"]=(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12,a13,a14)=>(dynCall_viiiiiiiiiiiiii=Module["dynCall_viiiiiiiiiiiiii"]=wasmExports["Zb"])(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12,a13,a14);var dynCall_viiiiiiiiiii=Module["dynCall_viiiiiiiiiii"]=(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11)=>(dynCall_viiiiiiiiiii=Module["dynCall_viiiiiiiiiii"]=wasmExports["_b"])(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11);var dynCall_viiiifii=Module["dynCall_viiiifii"]=(a0,a1,a2,a3,a4,a5,a6,a7)=>(dynCall_viiiifii=Module["dynCall_viiiifii"]=wasmExports["$b"])(a0,a1,a2,a3,a4,a5,a6,a7);var dynCall_fii=Module["dynCall_fii"]=(a0,a1,a2)=>(dynCall_fii=Module["dynCall_fii"]=wasmExports["ac"])(a0,a1,a2);var dynCall_v=Module["dynCall_v"]=a0=>(dynCall_v=Module["dynCall_v"]=wasmExports["bc"])(a0);var dynCall_viiiiiiiiii=Module["dynCall_viiiiiiiiii"]=(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10)=>(dynCall_viiiiiiiiii=Module["dynCall_viiiiiiiiii"]=wasmExports["cc"])(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10);var dynCall_viifi=Module["dynCall_viifi"]=(a0,a1,a2,a3,a4)=>(dynCall_viifi=Module["dynCall_viifi"]=wasmExports["dc"])(a0,a1,a2,a3,a4);var dynCall_fiii=Module["dynCall_fiii"]=(a0,a1,a2,a3)=>(dynCall_fiii=Module["dynCall_fiii"]=wasmExports["ec"])(a0,a1,a2,a3);var dynCall_viidi=Module["dynCall_viidi"]=(a0,a1,a2,a3,a4)=>(dynCall_viidi=Module["dynCall_viidi"]=wasmExports["fc"])(a0,a1,a2,a3,a4);var dynCall_dd=Module["dynCall_dd"]=(a0,a1)=>(dynCall_dd=Module["dynCall_dd"]=wasmExports["gc"])(a0,a1);var dynCall_iiijiiii=Module["dynCall_iiijiiii"]=(a0,a1,a2,a3,a4,a5,a6,a7,a8)=>(dynCall_iiijiiii=Module["dynCall_iiijiiii"]=wasmExports["hc"])(a0,a1,a2,a3,a4,a5,a6,a7,a8);var dynCall_viiijj=Module["dynCall_viiijj"]=(a0,a1,a2,a3,a4,a5,a6,a7)=>(dynCall_viiijj=Module["dynCall_viiijj"]=wasmExports["ic"])(a0,a1,a2,a3,a4,a5,a6,a7);var dynCall_iiiiiiidiiddii=Module["dynCall_iiiiiiidiiddii"]=(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12,a13)=>(dynCall_iiiiiiidiiddii=Module["dynCall_iiiiiiidiiddii"]=wasmExports["jc"])(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12,a13);var dynCall_jij=Module["dynCall_jij"]=(a0,a1,a2,a3)=>(dynCall_jij=Module["dynCall_jij"]=wasmExports["kc"])(a0,a1,a2,a3);var dynCall_jii=Module["dynCall_jii"]=(a0,a1,a2)=>(dynCall_jii=Module["dynCall_jii"]=wasmExports["lc"])(a0,a1,a2);var dynCall_jiji=Module["dynCall_jiji"]=(a0,a1,a2,a3,a4)=>(dynCall_jiji=Module["dynCall_jiji"]=wasmExports["mc"])(a0,a1,a2,a3,a4);var dynCall_iidiiii=Module["dynCall_iidiiii"]=(a0,a1,a2,a3,a4,a5,a6)=>(dynCall_iidiiii=Module["dynCall_iidiiii"]=wasmExports["nc"])(a0,a1,a2,a3,a4,a5,a6);var _ff_h264_cabac_tables=Module["_ff_h264_cabac_tables"]=115436;Module["ccall"]=ccall;Module["cwrap"]=cwrap;var calledRun;dependenciesFulfilled=function runCaller(){if(!calledRun)run();if(!calledRun)dependenciesFulfilled=runCaller};function callMain(){var entryFunction=_main;var argc=0;var argv=0;try{var ret=entryFunction(argc,argv);exitJS(ret,true);return ret}catch(e){return handleException(e)}}function run(){if(runDependencies>0){return}preRun();if(runDependencies>0){return}function doRun(){if(calledRun)return;calledRun=true;Module["calledRun"]=true;if(ABORT)return;initRuntime();preMain();if(Module["onRuntimeInitialized"])Module["onRuntimeInitialized"]();if(shouldRunNow)callMain();postRun()}if(Module["setStatus"]){Module["setStatus"]("Running...");setTimeout(function(){setTimeout(function(){Module["setStatus"]("")},1);doRun()},1)}else{doRun()}}if(Module["preInit"]){if(typeof Module["preInit"]=="function")Module["preInit"]=[Module["preInit"]];while(Module["preInit"].length>0){Module["preInit"].pop()()}}var shouldRunNow=true;if(Module["noInitialRun"])shouldRunNow=false;run();(function(){"use strict";const x={npc:cwrap("npc","number",["number"]),npd:cwrap("npd","number",["number"]),nps:cwrap("nps","number",["number","string"]),npt:cwrap("npt","number",["number"]),npu:cwrap("npu","number",["number","number"]),npg:cwrap("npg","string"),npv:cwrap("npv","number",["number","string","boolean"]),npm:cwrap("npm","number",["number","number"]),npb:cwrap("npb","number",["number","number"]),npk:cwrap("npk","number",["number","number"]),npe:cwrap("npe","number",["number","number"]),npr:cwrap("npr","number",["number","number","number","number"]),npy:cwrap("npy","number",["number","string"]),nor:cwrap("nor","number",["number","number","number"]),nar:cwrap("nar","number",["number"]),ncv:cwrap("ncv","number",["number"]),nuw:cwrap("nuw","number",["number","string"]),nea:cwrap("nea","number",["number","number"]),nev:cwrap("nev","number",["number","number"]),nsl:cwrap("nsl","number",["number","number","number","number"]),ncl:cwrap("ncl","number",["number","number","number","number"]),nsf:cwrap("nsf","number",["number"]),nas:cwrap("nas","number",["number"])};document.addEventListener("fullscreenchange",a=>{a.target.ctx&&x.nor(a.target.ctx)});class L extends _{constructor(){super(),this.ctx=x.npc(),this.version=x.npg(),this.isStart=!1,this.isVod=!1,this.isMSE=!1,this.isWCS=!1,this.hasAudio=!0,this.duration=0,this.volume=1,this.eventFlags=0,this.screenCount=0,this.connTimeout=0,this.connTimer=null,this.screenshotImage1=null,this.screenshotImage2=null,this.wakeLock=new te,NP[this.ctx].emit=this.emit.bind(this),this.on("buffer",e=>{this.connTimeout>0&&(this._clearTimeout(),e==="empty"&&(this.connTimer=setTimeout(()=>{this.emit("timeout")},this.connTimeout*1e3)))}),this.on("metadata",e=>{var t=D.parseScriptData(e.buffer,0,e.length);t.onMetaData&&(NP[this.ctx].framerate=t.onMetaData.framerate,NP[this.ctx].width=t.onMetaData.width,NP[this.ctx].height=t.onMetaData.height),t.onMetaData&&t.onMetaData.duration>0&&(NP[this.ctx].isVod=this.isVod=!0,NP[this.ctx].duration=this.duration=Math.floor(t.onMetaData.duration*1e3))})}setTimeout(e){e>0?this.connTimeout=e:this._clearTimeout()}_clearTimeout(){this.connTimer!=null&&(clearTimeout(this.connTimer),this.connTimer=null)}setView(e,t=!1,s="black"){return this.ve=document.getElementById(e),this.ve.style.backgroundColor=s,this.ve.ctx=this.ctx,NP[this.ctx].ve=this.ve,x.npv(this.ctx,"#"+e,t)}resizeView(e,t){x.npr(this.ctx,e,t,0)}setKeepScreenOn(){this.isKeepOn=!0}setScaleMode(e){return NP[this.ctx].scaleMode=e,x.npm(this.ctx,e)}setScaleLevel(e,t,s){return x.nsl(this.ctx,e,t,s)}setCanvasScaleLevel(e,t,s){return x.ncl(this.ctx,e,t,s)}setBufferTime(e){return x.npb(this.ctx,e)}skipLoopFilter(e){return x.npk(this.ctx,e)}skipErrorFrame(){x.nsf(this.ctx)}setVolume(e){this.volume=e,x.npe(this.ctx,e)}setCryptoKey(e){x.npy(this.ctx,e)}enableAudio(e){x.nea(this.ctx,e)}enableVideo(e){x.nev(this.ctx,e)}audioResume(){x.nar(this.ctx)}audioState(){let e=x.nas(this.ctx);return e===1?"running":e===2?"suspended":"closed"}launchIntoFullscreen(e){e.requestFullscreen?e.requestFullscreen():e.mozRequestFullScreen?e.mozRequestFullScreen():e.webkitRequestFullscreen?e.webkitRequestFullscreen():e.msRequestFullscreen&&e.msRequestFullscreen()}fullscreen(){this.launchIntoFullscreen(this.ve)}fullWebview(e){this.ve.tmpcss=this.ve.style.cssText,this.ve.style.cssText="width:100%;height:100%;position:absolute;top: 0;left: 0;z-index: -1;",x.nor(this.ctx,e)}exitFullWebview(){this.ve.tmpcss===void 0||this.ve.tmpcss===""||(this.ve.style.cssText=this.ve.tmpcss,x.nor(this.ctx,0))}onResize(e,t){x.nor(this.ctx,e,t)}screenshot(e,t,s){if(this.isStart&&this.screenCount++<30){if(this.screenshotImage1==null){this.screenshotImage1=this.ve.toDataURL("image/"+t,s),setTimeout(()=>{this.screenshot(e,t,s)},100);return}if(this.screenshotImage2==null&&(this.screenshotImage2=this.ve.toDataURL("image/"+t,s)),this.screenshotImage1.length===this.screenshotImage2.length){this.screenshotImage2=null,setTimeout(()=>{this.screenshot(e,t,s)},100);return}const i=document.createElement("a");i.href=this.screenshotImage1.length>this.screenshotImage2.length?this.screenshotImage1:this.screenshotImage2,i.download=e,i.click(),this.screenCount=0,this.screenshotImage1=null,this.screenshotImage2=null}else this.screenCount=0,this.screenshotImage1=null,this.screenshotImage2=null,this.emit("error","screenshot error")}start(e,t){if(typeof e!="string"){NP_ERROR("url invalid parameter");return}if(this.isStart){NP_DEBUG("The instance has started, ignore it");return}if(!this.ctx){NP_ERROR("Player has been released");return}NP_INFO("call start play url:",e),this.isKeepOn&&this.wakeLock.enable(),this.url=e,this.setVolume(this.volume),this.audioResume(),x.nps(this.ctx,this.url,t),this.isStart=!0}stop(){if(!this.isStart){NP_DEBUG("The instance has not started yet, ignore it");return}NP_INFO("call stop play url:",this.url),this.isKeepOn&&this.wakeLock.disable(),this.isStart=!1,this._clearTimeout(),x.npt(this.ctx),this.emit("stop")}pause(e){this.isStart&&(x.npu(this.ctx,e),this.isPause=e,NP[this.ctx].isPause=e)}clearView(){x.ncv(this.ctx)}release(e){!this.isMSE&&this.ctx&&(this.stop(),x.npd(this.ctx,e)),delete NP[this.ctx],this.ctx=null,this.wakeLock=null}useWASM(){let e=NP[this.ctx];e.isMSE=this.isMSE=!1,e.isWCS=this.isWCS=!1}useWorker(e){this.useWASM(),x.nuw(this.ctx,e)}useWCS(){if(this.isWCS="VideoDecoder"in window,this.isWCS){const e=["avc1.64002A","hev1.1.6.L123.b0"],t=[];let s=NP[this.ctx];for(const n of e)t.push({codec:n,codedWidth:1920,codedHeight:1080});const i=[];for(const n of t)try{const o=VideoDecoder.isConfigSupported(n);i.push(o)}catch(o){NP_ERROR(o)}Promise.all(i).then(n=>{n.forEach(o=>{o.config.codec[0]==="a"?s.isWCSSupportedH264=o.supported:s.isWCSSupportedH265=o.supported}),NP_INFO(`NodePlayer.js use WCS H264_Support=${s.isWCSSupportedH264} H265_Support=${s.isWCSSupportedH265}`),s.isWCS=!0,s.isMSE=!1})}else NP_INFO("WCS is not supported or experimental-web-platform-features not enabled")}useMSE(){if(this.isMSE="MediaSource"in window||"ManagedMediaSource"in window,this.isMSE){let e=NP[this.ctx];e.isMSESupportedH264=L.isMSESupported('video/mp4; codecs="avc1.64002A"'),e.isMSESupportedH265=L.isMSESupported('video/mp4; codecs="hev1.1.6.L123.b0"'),e.isMSE=!0,e.isWCS=!1,NP_INFO(`NodePlayer.js use MSE H264_Support=${e.isMSESupportedH264} H265_Support=${e.isMSESupportedH265}`)}else NP_INFO("MSE is not supported")}static getMediaSource(){if(window.ManagedMediaSource)return new window.ManagedMediaSource;if(window.MediaSource)return new window.MediaSource}static isMSESupported(e){return window.MediaSource&&window.MediaSource.isTypeSupported(e)||window.ManagedMediaSource&&window.ManagedMediaSource.isTypeSupported(e)}static detectLegacyChrome(){const t=navigator.userAgent.toLowerCase().match(/chrome\/([\d]+)./);return!!(t&&t[1]&&parseInt(t[1])<92)}static activeAudioEngine(e){window.activeAudioEngine=e,e&&NP_INFO("NodePlayer.js use activeAudioEngine")}static workletAudioEngine(e){window.workletAudioEngine=e&ae(),window.workletAudioEngine&&NP_INFO("NodePlayer.js use workletAudioEngine")}static debug(e){e?Module.logLevel=NP_LOGLEVEL.INFO:Module.logLevel=NP_LOGLEVEL.NONE}static load(e){window.npAllReadyFlag?e():setTimeout(L.load.bind(this,e),100)}static asyncLoad(){return new Promise((e,t)=>{L.load(()=>{e()})})}}class ee extends globalThis.HTMLElement{async connectedCallback(){NP_DEBUG("node-player connectedCallback"),await L.asyncLoad(),this.hasAttribute("activeAudioEngine")?L.activeAudioEngine(!0):this.hasAttribute("workletAudioEngine")&&L.workletAudioEngine(!0);const e=document.createElement("canvas");e.id="np-"+he(16),e.style="width:100%; height:100%",this.appendChild(e),this.src=this.getAttribute("src"),this.bufferTime=this.getAttribute("bufferTime")|1e3,this.scaleMode=this.getAttribute("scaleMode")|0,this.streamType=this.getAttribute("streamType"),this.cryptoKey=this.getAttribute("cryptoKey"),this.useMSE=this.hasAttribute("useMSE"),this.useWCS=this.hasAttribute("useWCS"),this.useWorker=this.hasAttribute("useWorker"),this.autoplay=this.hasAttribute("autoplay"),this.stats=this.hasAttribute("stats"),this.lastVolume=1,this.currentTimeTs=0,this.np=new L,this.np.on("error",s=>{this.dispatchEvent(new Event("error"))}),this.np.on("stats",s=>{this.stats&&NP_INFO(s)}),this.np.on("metadata",s=>{let i=D.parseScriptData(s.buffer,0,s.length);i.onMetaData&&(this.metaData=i.onMetaData,this.dispatchEvent(new Event("loadedmetadata")))}),this.np.on("videoFrame",s=>{let i=parseInt(s/1e3);this.currentTimeTs!=i&&(this.currentTimeTs=i,this.dispatchEvent(new Event("timeupdate")))}),this.np.on("buffer",s=>{s==="empty"?this.dispatchEvent(new Event("emptied")):s==="buffering"?this.dispatchEvent(new Event("waiting")):this.dispatchEvent(new Event("playing"))}),this.np.on("audioState",s=>{s||(this.muted=!0)}),this.useMSE?this.np.useMSE():this.useWCS?this.np.useWCS():this.useWorker&&this.np.useWorker(),this.np.setView(e.id),this.autoplay&&this.play(),new ResizeObserver(s=>{this.np.onResize()}).observe(e)}disconnectedCallback(){NP_DEBUG("node-player disconnectedCallback"),this.np.stop(),this.np.release(!0)}get paused(){return!this.hasAttribute("unpaused")}get muted(){return this.hasAttribute("muted")}set muted(e){this.np&&(this.toggleAttribute("muted",e),this.np.audioResume(),this.np.setVolume(e?0:this.lastVolume?this.lastVolume:1),this.dispatchEvent(new Event("volumechange")))}get volume(){return this.np?this.np.volume:1}set volume(e){this.np&&(this.np.audioResume(),this.np.setVolume(e),this.lastVolume=e,this.dispatchEvent(new Event("volumechange")))}get currentTime(){return this.currentTimeTs}set currentTime(e){}get duration(){return this.metaData&&this.metaData.duration?this.metaData.duration:1/0}play(){return this.toggleAttribute("unpaused",!0),this.dispatchEvent(new Event("playing")),this.np.isPause?this.np.pause(!1):this.np.isStart||(this.np.setBufferTime(this.bufferTime),this.np.setScaleMode(this.scaleMode),this.np.setCryptoKey(this.cryptoKey),this.np.setVolume(this.muted?0:1),this.np.start(this.src,this.streamType==="on-demand")),Promise.resolve()}pause(){this.np.pause(!0),this.np.audioResume(),this.toggleAttribute("unpaused",!1),this.dispatchEvent(new Event("pause"))}}globalThis.customElements&&!globalThis.customElements.get("node-player")&&globalThis.customElements.define("node-player",ee);class te{constructor(){this.isSupported="wakeLock"in navigator,this.wakeLock==null,this.isSupported?document.addEventListener("visibilitychange",async()=>{this.wakeLock!==null&&document.visibilityState==="visible"&&(this.wakeLock=await navigator.wakeLock.request("screen"))}):NP_DEBUG("Wake lock is not supported by this browser.")}async enable(){if(this.isSupported&&this.wakeLock==null)try{this.wakeLock=await navigator.wakeLock.request("screen")}catch(e){NP_DEBUG(`keepScreenOn ${e.name}, ${e.message}`)}}disable(){this.isSupported&&this.wakeLock!=null&&this.wakeLock.release().then(()=>{this.wakeLock=null})}}function _(){this._events=this._events||{},this._maxListeners=this._maxListeners||void 0}_.EventEmitter=_,_.prototype._events=void 0,_.prototype._maxListeners=void 0,_.defaultMaxListeners=10,_.prototype.setMaxListeners=function(a){if(!se(a)||a<0||isNaN(a))throw TypeError("n must be a positive number");return this._maxListeners=a,this},_.prototype.emit=function(a){let e,t,s,i,n,o;if(this._events||(this._events={}),a==="error"&&(!this._events.error||V(this._events.error)&&!this._events.error.length)){if(e=arguments[1],e instanceof Error)throw e;{const l=new Error('Uncaught, unspecified "error" event. ('+e+")");throw l.context=e,l}}if(t=this._events[a],G(t))return!1;if(O(t))switch(arguments.length){case 1:t.call(this);break;case 2:t.call(this,arguments[1]);break;case 3:t.call(this,arguments[1],arguments[2]);break;default:i=Array.prototype.slice.call(arguments,1),t.apply(this,i)}else if(V(t))for(i=Array.prototype.slice.call(arguments,1),o=t.slice(),s=o.length,n=0;n<s;n++)o[n].apply(this,i);return!0},_.prototype.addListener=function(a,e){let t;if(!O(e))throw TypeError("listener must be a function");return this._events||(this._events={}),this._events.newListener&&this.emit("newListener",a,O(e.listener)?e.listener:e),this._events[a]?V(this._events[a])?this._events[a].push(e):this._events[a]=[this._events[a],e]:this._events[a]=e,V(this._events[a])&&!this._events[a].warned&&(G(this._maxListeners)?t=_.defaultMaxListeners:t=this._maxListeners,t&&t>0&&this._events[a].length>t&&(this._events[a].warned=!0,console.error("(node) warning: possible EventEmitter memory leak detected. %d listeners added. Use emitter.setMaxListeners() to increase limit.",this._events[a].length),typeof console.trace=="function"&&console.trace())),this},_.prototype.on=_.prototype.addListener,_.prototype.once=function(a,e){if(!O(e))throw TypeError("listener must be a function");let t=!1;function s(){this.removeListener(a,s),t||(t=!0,e.apply(this,arguments))}return s.listener=e,this.on(a,s),this},_.prototype.removeListener=function(a,e){let t,s,i,n;if(!O(e))throw TypeError("listener must be a function");if(!this._events||!this._events[a])return this;if(t=this._events[a],i=t.length,s=-1,t===e||O(t.listener)&&t.listener===e)delete this._events[a],this._events.removeListener&&this.emit("removeListener",a,e);else if(V(t)){for(n=i;n-- >0;)if(t[n]===e||t[n].listener&&t[n].listener===e){s=n;break}if(s<0)return this;t.length===1?(t.length=0,delete this._events[a]):t.splice(s,1),this._events.removeListener&&this.emit("removeListener",a,e)}return this},_.prototype.removeAllListeners=function(a){let e,t;if(!this._events)return this;if(!this._events.removeListener)return arguments.length===0?this._events={}:this._events[a]&&delete this._events[a],this;if(arguments.length===0){for(e in this._events)e!=="removeListener"&&this.removeAllListeners(e);return this.removeAllListeners("removeListener"),this._events={},this}if(t=this._events[a],O(t))this.removeListener(a,t);else if(t)for(;t.length;)this.removeListener(a,t[t.length-1]);return delete this._events[a],this},_.prototype.listeners=function(a){let e;return!this._events||!this._events[a]?e=[]:O(this._events[a])?e=[this._events[a]]:e=this._events[a].slice(),e},_.prototype.listenerCount=function(a){if(this._events){const e=this._events[a];if(O(e))return 1;if(e)return e.length}return 0},_.listenerCount=function(a,e){return a.listenerCount(e)};function O(a){return typeof a=="function"}function se(a){return typeof a=="number"}function V(a){return typeof a=="object"&&a!==null}function G(a){return a==="undefine"}function z(a,e,t){const s=a;if(e+t<s.length){for(;t--;)if((s[++e]&192)!==128)return!1;return!0}else return!1}function q(a){const e=[],t=a;let s=0;const i=a.length;for(;s<i;){if(t[s]<128){e.push(String.fromCharCode(t[s])),++s;continue}else if(!(t[s]<192)){if(t[s]<224){if(z(t,s,1)){const n=(t[s]&31)<<6|t[s+1]&63;if(n>=128){e.push(String.fromCharCode(n&65535)),s+=2;continue}}}else if(t[s]<240){if(z(t,s,2)){const n=(t[s]&15)<<12|(t[s+1]&63)<<6|t[s+2]&63;if(n>=2048&&(n&63488)!==55296){e.push(String.fromCharCode(n&65535)),s+=3;continue}}}else if(t[s]<248&&z(t,s,3)){let n=(t[s]&7)<<18|(t[s+1]&63)<<12|(t[s+2]&63)<<6|t[s+3]&63;if(n>65536&&n<1114112){n-=65536,e.push(String.fromCharCode(n>>>10|55296)),e.push(String.fromCharCode(n&1023|56320)),s+=4;continue}}}e.push("�"),++s}return e.join("")}const k=function(){const a=new ArrayBuffer(2);return new DataView(a).setInt16(0,256,!0),new Int16Array(a)[0]===256}();class D{static parseScriptData(e,t,s){const i={};try{const n=D.parseValue(e,t,s),o=D.parseValue(e,t+n.size,s-n.size);i[n.data]=o.data}catch(n){NP_ERROR("AMF",n.toString())}return i}static parseObject(e,t,s){if(s<3)throw new A("Data not enough when parse ScriptDataObject");const i=D.parseString(e,t,s),n=D.parseValue(e,t+i.size,s-i.size),o=n.objectEnd;return{data:{name:i.data,value:n.data},size:i.size+n.size,objectEnd:o}}static parseVariable(e,t,s){return D.parseObject(e,t,s)}static parseString(e,t,s){if(s<2)throw new A("Data not enough when parse String");const n=new DataView(e,t,s).getUint16(0,!k);let o;return n>0?o=q(new Uint8Array(e,t+2,n)):o="",{data:o,size:2+n}}static parseLongString(e,t,s){if(s<4)throw new A("Data not enough when parse LongString");const n=new DataView(e,t,s).getUint32(0,!k);let o;return n>0?o=q(new Uint8Array(e,t+4,n)):o="",{data:o,size:4+n}}static parseDate(e,t,s){if(s<10)throw new A("Data size invalid when parse Date");const i=new DataView(e,t,s);let n=i.getFloat64(0,!k);const o=i.getInt16(8,!k);return n+=o*60*1e3,{data:new Date(n),size:10}}static parseValue(e,t,s){if(s<1)throw new A("Data not enough when parse Value");const i=new DataView(e,t,s);let n=1;const o=i.getUint8(0);let l,w=!1;try{switch(o){case 0:l=i.getFloat64(1,!k),n+=8;break;case 1:{l=!!i.getUint8(1),n+=1;break}case 2:{const u=D.parseString(e,t+1,s-1);l=u.data,n+=u.size;break}case 3:{l={};let u=0;for((i.getUint32(s-4,!k)&16777215)===9&&(u=3);n<s-4;){const h=D.parseObject(e,t+n,s-n-u);if(h.objectEnd)break;l[h.data.name]=h.data.value,n+=h.size}n<=s-3&&(i.getUint32(n-1,!k)&16777215)===9&&(n+=3);break}case 8:{l={},n+=4;let u=0;for((i.getUint32(s-4,!k)&16777215)===9&&(u=3);n<s-8;){const h=D.parseVariable(e,t+n,s-n-u);if(h.objectEnd)break;l[h.data.name]=h.data.value,n+=h.size}n<=s-3&&(i.getUint32(n-1,!k)&16777215)===9&&(n+=3);break}case 9:l=void 0,n=1,w=!0;break;case 10:{l=[];const u=i.getUint32(1,!k);n+=4;for(let h=0;h<u;h++){const F=D.parseValue(e,t+n,s-n);l.push(F.data),n+=F.size}break}case 11:{const u=D.parseDate(e,t+1,s-1);l=u.data,n+=u.size;break}case 12:{const u=D.parseString(e,t+1,s-1);l=u.data,n+=u.size;break}default:n=s,NP_ERROR("AMF","Unsupported AMF value type "+o)}}catch(u){NP_ERROR("AMF",u.toString())}return{data:l,size:n,objectEnd:w}}}class r{static init(){r.types={avc1:[],avcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],hvc1:[],hvcC:[],av01:[],av1C:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],vmhd:[],mvex:[],mvhd:[],sdtp:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[]};for(let t in r.types)r.types.hasOwnProperty(t)&&(r.types[t]=[t.charCodeAt(0),t.charCodeAt(1),t.charCodeAt(2),t.charCodeAt(3)]);let e=r.constants={};e.FTYP=new Uint8Array([105,115,111,109,0,0,0,1,105,115,111,109,97,118,99,49]),e.STSD_PREFIX=new Uint8Array([0,0,0,0,0,0,0,1]),e.STTS=new Uint8Array([0,0,0,0,0,0,0,0]),e.STSC=e.STCO=e.STTS,e.STSZ=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),e.HDLR_VIDEO=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),e.DREF=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),e.VMHD=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0])}static box(e){let t=8,s=null,i=Array.prototype.slice.call(arguments,1),n=i.length;for(let l=0;l<n;l++)t+=i[l].byteLength;s=new Uint8Array(t),s[0]=t>>>24&255,s[1]=t>>>16&255,s[2]=t>>>8&255,s[3]=t&255,s.set(e,4);let o=8;for(let l=0;l<n;l++)s.set(i[l],o),o+=i[l].byteLength;return s}static generateInitSegment(e){let t=r.box(r.types.ftyp,r.constants.FTYP),s=r.moov(e),i=new Uint8Array(t.byteLength+s.byteLength);return i.set(t,0),i.set(s,t.byteLength),i}static moov(e){let t=r.mvhd(e.timescale,e.duration),s=r.trak(e),i=r.mvex(e);return r.box(r.types.moov,t,s,i)}static mvhd(e,t){return r.box(r.types.mvhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,e>>>24&255,e>>>16&255,e>>>8&255,e&255,t>>>24&255,t>>>16&255,t>>>8&255,t&255,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]))}static trak(e){return r.box(r.types.trak,r.tkhd(e),r.mdia(e))}static tkhd(e){let t=e.id,s=e.duration,i=e.presentWidth,n=e.presentHeight;return r.box(r.types.tkhd,new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,t&255,0,0,0,0,s>>>24&255,s>>>16&255,s>>>8&255,s&255,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,i>>>8&255,i&255,0,0,n>>>8&255,n&255,0,0]))}static mdia(e){return r.box(r.types.mdia,r.mdhd(e),r.hdlr(e),r.minf(e))}static mdhd(e){let t=e.timescale,s=e.duration;return r.box(r.types.mdhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,t&255,s>>>24&255,s>>>16&255,s>>>8&255,s&255,85,196,0,0]))}static hdlr(e){let t=r.constants.HDLR_VIDEO;return r.box(r.types.hdlr,t)}static minf(e){let t=r.box(r.types.vmhd,r.constants.VMHD);return r.box(r.types.minf,t,r.dinf(),r.stbl(e))}static dinf(){return r.box(r.types.dinf,r.box(r.types.dref,r.constants.DREF))}static stbl(e){return r.box(r.types.stbl,r.stsd(e),r.box(r.types.stts,r.constants.STTS),r.box(r.types.stsc,r.constants.STSC),r.box(r.types.stsz,r.constants.STSZ),r.box(r.types.stco,r.constants.STCO))}static stsd(e){return e.codec.startsWith("hvc1")?r.box(r.types.stsd,r.constants.STSD_PREFIX,r.hvc1(e)):e.codec.startsWith("av01")?r.box(r.types.stsd,r.constants.STSD_PREFIX,r.av01(e)):r.box(r.types.stsd,r.constants.STSD_PREFIX,r.avc1(e))}static avc1(e){let t=e.avcc,s=e.codecWidth,i=e.codecHeight,n=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,s>>>8&255,s&255,i>>>8&255,i&255,0,72,0,0,0,72,0,0,0,0,0,0,0,1,10,120,113,113,47,102,108,118,46,106,115,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,255,255]);return r.box(r.types.avc1,n,r.box(r.types.avcC,t))}static hvc1(e){let t=e.hvcc,s=e.codecWidth,i=e.codecHeight,n=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,s>>>8&255,s&255,i>>>8&255,i&255,0,72,0,0,0,72,0,0,0,0,0,0,0,1,10,120,113,113,47,102,108,118,46,106,115,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,255,255]);return r.box(r.types.hvc1,n,r.box(r.types.hvcC,t))}static av01(e){let t=e.av1c,s=e.codecWidth||192,i=e.codecHeight||108,n=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,s>>>8&255,s&255,i>>>8&255,i&255,0,72,0,0,0,72,0,0,0,0,0,0,0,1,10,120,113,113,47,102,108,118,46,106,115,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,255,255]);return r.box(r.types.av01,n,r.box(r.types.av1C,t))}static mvex(e){return r.box(r.types.mvex,r.trex(e))}static trex(e){let t=e.id,s=new Uint8Array([0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,t&255,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]);return r.box(r.types.trex,s)}static moof(e,t){return r.box(r.types.moof,r.mfhd(e.sequenceNumber),r.traf(e,t))}static mfhd(e){let t=new Uint8Array([0,0,0,0,e>>>24&255,e>>>16&255,e>>>8&255,e&255]);return r.box(r.types.mfhd,t)}static traf(e,t){let s=e.id,i=r.box(r.types.tfhd,new Uint8Array([0,0,0,0,s>>>24&255,s>>>16&255,s>>>8&255,s&255])),n=r.box(r.types.tfdt,new Uint8Array([0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,t&255])),o=r.sdtp(e),l=r.trun(e,o.byteLength+16+16+8+16+8+8);return r.box(r.types.traf,i,n,l,o)}static sdtp(e){let t=e.samples||[],s=t.length,i=new Uint8Array(4+s);for(let n=0;n<s;n++){let o=t[n].flags;i[n+4]=o.isLeading<<6|o.dependsOn<<4|o.isDependedOn<<2|o.hasRedundancy}return r.box(r.types.sdtp,i)}static trun(e,t){let s=e.samples||[],i=s.length,n=12+16*i,o=new Uint8Array(n);t+=8+n,o.set([0,0,15,1,i>>>24&255,i>>>16&255,i>>>8&255,i&255,t>>>24&255,t>>>16&255,t>>>8&255,t&255],0);for(let l=0;l<i;l++){let w=s[l].duration,u=s[l].size,h=s[l].flags,F=s[l].cts;o.set([w>>>24&255,w>>>16&255,w>>>8&255,w&255,u>>>24&255,u>>>16&255,u>>>8&255,u&255,h.isLeading<<2|h.dependsOn,h.isDependedOn<<6|h.hasRedundancy<<4|h.isNonSync,0,0,F>>>24&255,F>>>16&255,F>>>8&255,F&255],12+16*l)}return r.box(r.types.trun,o)}static mdat(e){return r.box(r.types.mdat,e)}}r.init();const U={ERROR:"error",SOURCE_OPEN:"source_open",UPDATE_END:"update_end",BUFFER_FULL:"buffer_full",START_STREAMING:"start_streaming",END_STREAMING:"end_streaming"};class ie{constructor(e){this._message=e}get name(){return"RuntimeException"}get message(){return this._message}toString(){return this.name+": "+this.message}}class A extends ie{constructor(e){super(e)}get name(){return"IllegalStateException"}}class W{constructor(e,t,s,i,n){this.dts=e,this.pts=t,this.duration=s,this.originalDts=i,this.isSyncPoint=n,this.fileposition=null}}class K{constructor(){this.beginDts=0,this.endDts=0,this.beginPts=0,this.endPts=0,this.originalBeginDts=0,this.originalEndDts=0,this.syncPoints=[],this.firstSample=null,this.lastSample=null}appendSyncPoint(e){e.isSyncPoint=!0,this.syncPoints.push(e)}}class P{constructor(e){this._type=e,this._list=[],this._lastAppendLocation=-1}get type(){return this._type}get length(){return this._list.length}isEmpty(){return this._list.length===0}clear(){this._list=[],this._lastAppendLocation=-1}_searchNearestSegmentBefore(e){let t=this._list;if(t.length===0)return-2;let s=t.length-1,i=0,n=0,o=s,l=0;if(e<t[0].originalBeginDts)return l=-1,l;for(;n<=o;)if(i=n+Math.floor((o-n)/2),i===s||e>t[i].lastSample.originalDts&&e<t[i+1].originalBeginDts){l=i;break}else t[i].originalBeginDts<e?n=i+1:o=i-1;return l}_searchNearestSegmentAfter(e){return this._searchNearestSegmentBefore(e)+1}append(e){let t=this._list,s=e,i=this._lastAppendLocation,n=0;i!==-1&&i<t.length&&s.originalBeginDts>=t[i].lastSample.originalDts&&(i===t.length-1||i<t.length-1&&s.originalBeginDts<t[i+1].originalBeginDts)?n=i+1:t.length>0&&(n=this._searchNearestSegmentBefore(s.originalBeginDts)+1),this._lastAppendLocation=n,this._list.splice(n,0,s)}getLastSegmentBefore(e){let t=this._searchNearestSegmentBefore(e);return t>=0?this._list[t]:null}getLastSampleBefore(e){let t=this.getLastSegmentBefore(e);return t!=null?t.lastSample:null}getLastSyncPointBefore(e){let t=this._searchNearestSegmentBefore(e),s=this._list[t].syncPoints;for(;s.length===0&&t>0;)t--,s=this._list[t].syncPoints;return s.length>0?s[s.length-1]:null}}class ne{constructor(e){this.TAG="MP4Remuxer",this._config=e,this._isLive=e.isLive===!0,this._dtsBase=-1,this._dtsBaseInited=!1,this._audioDtsBase=1/0,this._videoDtsBase=1/0,this._audioNextDts=void 0,this._videoNextDts=void 0,this._audioStashedLastSample=null,this._videoStashedLastSample=null,this._audioMeta=null,this._videoMeta=null,this._audioSegmentInfoList=new P("audio"),this._videoSegmentInfoList=new P("video"),this._onInitSegment=null,this._onMediaSegment=null,this._forceFirstIDR=!!(Browser.chrome&&(Browser.version.major<50||Browser.version.major===50&&Browser.version.build<2661)),this._fillSilentAfterSeek=Browser.msedge||Browser.msie,this._mp3UseMpegAudio=!Browser.firefox,this._fillAudioTimestampGap=this._config.fixAudioTimestampGap}destroy(){this._dtsBase=-1,this._dtsBaseInited=!1,this._audioMeta=null,this._videoMeta=null,this._audioSegmentInfoList.clear(),this._audioSegmentInfoList=null,this._videoSegmentInfoList.clear(),this._videoSegmentInfoList=null,this._onInitSegment=null,this._onMediaSegment=null}bindDataSource(e){return e.onDataAvailable=this.remux.bind(this),e.onTrackMetadata=this._onTrackMetadataReceived.bind(this),this}get onInitSegment(){return this._onInitSegment}set onInitSegment(e){this._onInitSegment=e}get onMediaSegment(){return this._onMediaSegment}set onMediaSegment(e){this._onMediaSegment=e}insertDiscontinuity(){this._audioNextDts=this._videoNextDts=void 0}seek(e){this._audioStashedLastSample=null,this._videoStashedLastSample=null,this._videoSegmentInfoList.clear(),this._audioSegmentInfoList.clear()}remux(e,t){if(!this._onMediaSegment)throw new A("MP4Remuxer: onMediaSegment callback must be specificed!");this._dtsBaseInited||this._calculateDtsBase(e,t),t&&this._remuxVideo(t),e&&this._remuxAudio(e)}_onTrackMetadataReceived(e,t){let s=null,i="mp4",n=t.codec;if(e==="audio")this._audioMeta=t,t.codec==="mp3"&&this._mp3UseMpegAudio?(i="mpeg",n="",s=new Uint8Array):s=r.generateInitSegment(t);else if(e==="video")this._videoMeta=t,s=r.generateInitSegment(t);else return;if(!this._onInitSegment)throw new A("MP4Remuxer: onInitSegment callback must be specified!");this._onInitSegment(e,{type:e,data:s.buffer,codec:n,container:`${e}/${i}`,mediaDuration:t.duration})}_calculateDtsBase(e,t){this._dtsBaseInited||(e&&e.samples&&e.samples.length&&(this._audioDtsBase=e.samples[0].dts),t&&t.samples&&t.samples.length&&(this._videoDtsBase=t.samples[0].dts),this._dtsBase=Math.min(this._audioDtsBase,this._videoDtsBase),this._dtsBaseInited=!0)}getTimestampBase(){if(this._dtsBaseInited)return this._dtsBase}flushStashedSamples(){let e=this._videoStashedLastSample,t=this._audioStashedLastSample,s={type:"video",id:1,sequenceNumber:0,samples:[],length:0};e!=null&&(s.samples.push(e),s.length=e.length);let i={type:"audio",id:2,sequenceNumber:0,samples:[],length:0};t!=null&&(i.samples.push(t),i.length=t.length),this._videoStashedLastSample=null,this._audioStashedLastSample=null,this._remuxVideo(s,!0),this._remuxAudio(i,!0)}_remuxAudio(e,t){if(this._audioMeta==null)return;let s=e,i=s.samples,n,o=-1,l=-1,u=this._audioMeta.refSampleDuration,h=this._audioMeta.codec==="mp3"&&this._mp3UseMpegAudio,F=this._dtsBaseInited&&this._audioNextDts===void 0,B=!1;if(!i||i.length===0||i.length===1&&!t)return;let C=0,R=null,m=0;h?(C=0,m=s.length):(C=8,m=8+s.length);let p=null;if(i.length>1&&(p=i.pop(),m-=p.length),this._audioStashedLastSample!=null){let c=this._audioStashedLastSample;this._audioStashedLastSample=null,i.unshift(c),m+=c.length}p!=null&&(this._audioStashedLastSample=p);let b=i[0].dts-this._dtsBase;if(this._audioNextDts)n=b-this._audioNextDts;else if(this._audioSegmentInfoList.isEmpty())n=0,this._fillSilentAfterSeek&&!this._videoSegmentInfoList.isEmpty()&&this._audioMeta.originalCodec!=="mp3"&&(B=!0);else{let c=this._audioSegmentInfoList.getLastSampleBefore(b);if(c!=null){let v=b-(c.originalDts+c.duration);v<=3&&(v=0);let I=c.dts+c.duration+v;n=b-I}else n=0}if(B){let c=b-n,v=this._videoSegmentInfoList.getLastSegmentBefore(b);if(v!=null&&v.beginDts<c){let I=AAC.getSilentFrame(this._audioMeta.originalCodec,this._audioMeta.channelCount);if(I){let S=v.beginDts,y=c-v.beginDts;Log.v(this.TAG,`InsertPrefixSilentAudio: dts: ${S}, duration: ${y}`),i.unshift({unit:I,dts:S,pts:S}),m+=I.byteLength}}else B=!1}let g=[];for(let c=0;c<i.length;c++){let v=i[c],I=v.unit,S=v.dts-this._dtsBase,y=S,Q=!1,$=null,M=0;if(!(S<-.001)){if(this._audioMeta.codec!=="mp3"){let E=S;const X=3;if(this._audioNextDts&&(E=this._audioNextDts),n=S-E,n<=-X*u){Log.w(this.TAG,`Dropping 1 audio frame (originalDts: ${S} ms ,curRefDts: ${E} ms)  due to dtsCorrection: ${n} ms overlap.`);continue}else if(n>=X*u&&this._fillAudioTimestampGap&&!Browser.safari){Q=!0;let Y=Math.floor(n/u);Log.w(this.TAG,`Large audio timestamp gap detected, may cause AV sync to drift. Silent frames will be generated to avoid unsync.\noriginalDts: ${S} ms, curRefDts: ${E} ms, dtsCorrection: ${Math.round(n)} ms, generate: ${Y} frames`),y=Math.floor(E),M=Math.floor(E+u)-y;let j=AAC.getSilentFrame(this._audioMeta.originalCodec,this._audioMeta.channelCount);j==null&&(Log.w(this.TAG,`Unable to generate silent frame for ${this._audioMeta.originalCodec} with ${this._audioMeta.channelCount} channels, repeat last frame`),j=I),$=[];for(let Z=0;Z<Y;Z++){E=E+u;let H=Math.floor(E),de=Math.floor(E+u)-H,J={dts:H,pts:H,cts:0,unit:j,size:j.byteLength,duration:de,originalDts:S,flags:{isLeading:0,dependsOn:1,isDependedOn:0,hasRedundancy:0}};$.push(J),m+=J.size}this._audioNextDts=E+u}else y=Math.floor(E),M=Math.floor(E+u)-y,this._audioNextDts=E+u}else y=S-n,c!==i.length-1?M=i[c+1].dts-this._dtsBase-n-y:p!=null?M=p.dts-this._dtsBase-n-y:g.length>=1?M=g[g.length-1].duration:M=Math.floor(u),this._audioNextDts=y+M;o===-1&&(o=y),g.push({dts:y,pts:y,cts:0,unit:v.unit,size:v.unit.byteLength,duration:M,originalDts:S,flags:{isLeading:0,dependsOn:1,isDependedOn:0,hasRedundancy:0}}),Q&&g.push.apply(g,$)}}if(g.length===0){s.samples=[],s.length=0;return}h?R=new Uint8Array(m):(R=new Uint8Array(m),R[0]=m>>>24&255,R[1]=m>>>16&255,R[2]=m>>>8&255,R[3]=m&255,R.set(r.types.mdat,4));for(let c=0;c<g.length;c++){let v=g[c].unit;R.set(v,C),C+=v.byteLength}let d=g[g.length-1];l=d.dts+d.duration;let f=new K;f.beginDts=o,f.endDts=l,f.beginPts=o,f.endPts=l,f.originalBeginDts=g[0].originalDts,f.originalEndDts=d.originalDts+d.duration,f.firstSample=new W(g[0].dts,g[0].pts,g[0].duration,g[0].originalDts,!1),f.lastSample=new W(d.dts,d.pts,d.duration,d.originalDts,!1),this._isLive||this._audioSegmentInfoList.append(f),s.samples=g,s.sequenceNumber++;let T=null;h?T=new Uint8Array:T=r.moof(s,o),s.samples=[],s.length=0;let N={type:"audio",data:this._mergeBoxes(T,R).buffer,sampleCount:g.length,info:f};h&&F&&(N.timestampOffset=o),this._onMediaSegment("audio",N)}_remuxVideo(e,t){if(this._videoMeta==null)return;let s=e,i=s.samples,n,o=-1,l=-1,w=-1,u=-1;if(!i||i.length===0||i.length===1&&!t)return;let h=8,F=null,B=8+e.length,C=null;if(i.length>1&&(C=i.pop(),B-=C.length),this._videoStashedLastSample!=null){let d=this._videoStashedLastSample;this._videoStashedLastSample=null,i.unshift(d),B+=d.length}C!=null&&(this._videoStashedLastSample=C);let R=i[0].dts-this._dtsBase;if(this._videoNextDts)n=R-this._videoNextDts;else if(this._videoSegmentInfoList.isEmpty())n=0;else{let d=this._videoSegmentInfoList.getLastSampleBefore(R);if(d!=null){let f=R-(d.originalDts+d.duration);f<=3&&(f=0);let T=d.dts+d.duration+f;n=R-T}else n=0}let m=new K,p=[];for(let d=0;d<i.length;d++){let f=i[d],T=f.dts-this._dtsBase,N=f.isKeyframe,c=T-n,v=f.cts,I=c+v;o===-1&&(o=c,w=I);let S=0;if(d!==i.length-1?S=i[d+1].dts-this._dtsBase-n-c:C!=null?S=C.dts-this._dtsBase-n-c:p.length>=1?S=p[p.length-1].duration:S=Math.floor(this._videoMeta.refSampleDuration),N){let y=new W(c,I,S,f.dts,!0);y.fileposition=f.fileposition,m.appendSyncPoint(y)}p.push({dts:c,pts:I,cts:v,units:f.units,size:f.length,isKeyframe:N,duration:S,originalDts:T,flags:{isLeading:0,dependsOn:N?2:1,isDependedOn:N?1:0,hasRedundancy:0,isNonSync:N?0:1}})}F=new Uint8Array(B),F[0]=B>>>24&255,F[1]=B>>>16&255,F[2]=B>>>8&255,F[3]=B&255,F.set(r.types.mdat,4);for(let d=0;d<p.length;d++){let f=p[d].units;for(;f.length;){let N=f.shift().data;F.set(N,h),h+=N.byteLength}}let b=p[p.length-1];if(l=b.dts+b.duration,u=b.pts+b.duration,this._videoNextDts=l,m.beginDts=o,m.endDts=l,m.beginPts=w,m.endPts=u,m.originalBeginDts=p[0].originalDts,m.originalEndDts=b.originalDts+b.duration,m.firstSample=new W(p[0].dts,p[0].pts,p[0].duration,p[0].originalDts,p[0].isKeyframe),m.lastSample=new W(b.dts,b.pts,b.duration,b.originalDts,b.isKeyframe),this._isLive||this._videoSegmentInfoList.append(m),s.samples=p,s.sequenceNumber++,this._forceFirstIDR){let d=p[0].flags;d.dependsOn=2,d.isNonSync=0}let g=r.moof(s,o);s.samples=[],s.length=0,this._onMediaSegment("video",{type:"video",data:this._mergeBoxes(g,F).buffer,sampleCount:p.length,info:m})}_mergeBoxes(e,t){let s=new Uint8Array(e.byteLength+t.byteLength);return s.set(e,0),s.set(t,e.byteLength),s}}class re{constructor(){this._config={autoCleanupSourceBuffer:!0,autoCleanupMaxBackwardDuration:30,autoCleanupMinBackwardDuration:10},this._emitter=new _,this.e={onSourceOpen:this._onSourceOpen.bind(this),onSourceEnded:this._onSourceEnded.bind(this),onSourceClose:this._onSourceClose.bind(this),onStartStreaming:this._onStartStreaming.bind(this),onEndStreaming:this._onEndStreaming.bind(this),onQualityChange:this._onQualityChange.bind(this),onSourceBufferError:this._onSourceBufferError.bind(this),onSourceBufferUpdateEnd:this._onSourceBufferUpdateEnd.bind(this)},this._useManagedMediaSource="ManagedMediaSource"in self&&!("MediaSource"in self),this._mediaSource=null,this._mediaSourceObjectURL=null,this._mediaElementProxy=null,this._isBufferFull=!1,this._hasPendingEos=!1,this._requireSetMediaDuration=!1,this._pendingMediaDuration=0,this._pendingSourceBufferInit=[],this._mimeTypes={video:null,audio:null},this._sourceBuffers={video:null,audio:null},this._lastInitSegments={video:null,audio:null},this._pendingSegments={video:[],audio:[]},this._pendingRemoveRanges={video:[],audio:[]}}destroy(){this._mediaSource&&this.shutdown(),this._mediaSourceObjectURL&&this.revokeObjectURL(),this.e=null,this._emitter.removeAllListeners(),this._emitter=null}on(e,t){this._emitter.addListener(e,t)}off(e,t){this._emitter.removeListener(e,t)}initialize(e){if(this._mediaSource)throw new A("MediaSource has been attached to an HTMLMediaElement!");this._useManagedMediaSource&&NP_INFO("Using ManagedMediaSource");let t=this._mediaSource=this._useManagedMediaSource?new self.ManagedMediaSource:new self.MediaSource;t.addEventListener("sourceopen",this.e.onSourceOpen),t.addEventListener("sourceended",this.e.onSourceEnded),t.addEventListener("sourceclose",this.e.onSourceClose),this._useManagedMediaSource&&(t.addEventListener("startstreaming",this.e.onStartStreaming),t.addEventListener("endstreaming",this.e.onEndStreaming),t.addEventListener("qualitychange",this.e.onQualityChange)),this._mediaElementProxy=e}shutdown(){if(this._mediaSource){let e=this._mediaSource;for(let t in this._sourceBuffers){let s=this._pendingSegments[t];s.splice(0,s.length),this._pendingSegments[t]=null,this._pendingRemoveRanges[t]=null,this._lastInitSegments[t]=null;let i=this._sourceBuffers[t];if(i){if(e.readyState!=="closed"){try{e.removeSourceBuffer(i)}catch(n){NP_ERROR(n.message)}i.removeEventListener("error",this.e.onSourceBufferError),i.removeEventListener("updateend",this.e.onSourceBufferUpdateEnd)}this._mimeTypes[t]=null,this._sourceBuffers[t]=null}}if(e.readyState==="open")try{e.endOfStream()}catch(t){NP_ERROR(t.message)}this._mediaElementProxy=null,e.removeEventListener("sourceopen",this.e.onSourceOpen),e.removeEventListener("sourceended",this.e.onSourceEnded),e.removeEventListener("sourceclose",this.e.onSourceClose),this._useManagedMediaSource&&(e.removeEventListener("startstraming",this.e.onStartStreaming),e.removeEventListener("endstreaming",this.e.onEndStreaming),e.removeEventListener("qualitychange",this.e.onQualityChange)),this._pendingSourceBufferInit=[],this._isBufferFull=!1,this._mediaSource=null}}isManagedMediaSource(){return this._useManagedMediaSource}getObject(){if(!this._mediaSource)throw new A("MediaSource has not been initialized yet!");return this._mediaSource}getHandle(){if(!this._mediaSource)throw new A("MediaSource has not been initialized yet!");return this._mediaSource.handle}getObjectURL(){if(!this._mediaSource)throw new A("MediaSource has not been initialized yet!");return this._mediaSourceObjectURL==null&&(this._mediaSourceObjectURL=URL.createObjectURL(this._mediaSource)),this._mediaSourceObjectURL}revokeObjectURL(){this._mediaSourceObjectURL&&(URL.revokeObjectURL(this._mediaSourceObjectURL),this._mediaSourceObjectURL=null)}appendInitSegment(e,t=void 0){if(!this._mediaSource||this._mediaSource.readyState!=="open"||this._mediaSource.streaming===!1){this._pendingSourceBufferInit.push(e),this._pendingSegments[e.type].push(e);return}let s=e,i=`video/mp4;codecs=${s.codec}`,n=!1;if(NP_INFO("Received Initialization Segment, mimeType: "+i),this._lastInitSegments[s.type]=s,i!==this._mimeTypes[s.type]){if(this._mimeTypes[s.type])NP_INFO(`Notice: ${s.type} mimeType changed, origin: ${this._mimeTypes[s.type]}, target: ${i}`);else{n=!0;try{let o=this._sourceBuffers[s.type]=this._mediaSource.addSourceBuffer(i);o.addEventListener("error",this.e.onSourceBufferError),o.addEventListener("updateend",this.e.onSourceBufferUpdateEnd)}catch(o){NP_ERROR(o.message),this._emitter.emit(U.ERROR,{code:o.code,msg:o.message});return}}this._mimeTypes[s.type]=i}t||this._pendingSegments[s.type].push(s),n||this._sourceBuffers[s.type]&&!this._sourceBuffers[s.type].updating&&this._doAppendSegments()}appendMediaSegment(e){let t=e;this._pendingSegments[t.type].push(t),this._config.autoCleanupSourceBuffer&&this._needCleanupSourceBuffer()&&this._doCleanupSourceBuffer();let s=this._sourceBuffers[t.type];s&&!s.updating&&!this._hasPendingRemoveRanges()&&this._doAppendSegments()}flush(){for(let e in this._sourceBuffers){if(!this._sourceBuffers[e])continue;let t=this._sourceBuffers[e];if(this._mediaSource.readyState==="open")try{t.abort()}catch(i){NP_ERROR(i.message)}let s=this._pendingSegments[e];if(s.splice(0,s.length),this._mediaSource.readyState!=="closed"){for(let i=0;i<t.buffered.length;i++){let n=t.buffered.start(i),o=t.buffered.end(i);this._pendingRemoveRanges[e].push({start:n,end:o})}t.updating||this._doRemoveRanges()}}}endOfStream(){let e=this._mediaSource,t=this._sourceBuffers;if(!e||e.readyState!=="open"){e&&e.readyState==="closed"&&this._hasPendingSegments()&&(this._hasPendingEos=!0);return}t.video&&t.video.updating||t.audio&&t.audio.updating?this._hasPendingEos=!0:(this._hasPendingEos=!1,e.endOfStream())}_needCleanupSourceBuffer(){if(!this._config.autoCleanupSourceBuffer)return!1;let e=this._mediaElementProxy.getCurrentTime();for(let t in this._sourceBuffers){let s=this._sourceBuffers[t];if(s){let i=s.buffered;if(i.length>=1&&e-i.start(0)>=this._config.autoCleanupMaxBackwardDuration)return!0}}return!1}_doCleanupSourceBuffer(){let e=this._mediaElementProxy.getCurrentTime();for(let t in this._sourceBuffers){let s=this._sourceBuffers[t];if(s){let i=s.buffered,n=!1;for(let o=0;o<i.length;o++){let l=i.start(o),w=i.end(o);if(l<=e&&e<w+3){if(e-l>=this._config.autoCleanupMaxBackwardDuration){n=!0;let u=e-this._config.autoCleanupMinBackwardDuration;this._pendingRemoveRanges[t].push({start:l,end:u})}}else w<e&&(n=!0,this._pendingRemoveRanges[t].push({start:l,end:w}))}n&&!s.updating&&this._doRemoveRanges()}}}_updateMediaSourceDuration(){let e=this._sourceBuffers;if(this._mediaElementProxy.getReadyState()===0||this._mediaSource.readyState!=="open"||e.video&&e.video.updating||e.audio&&e.audio.updating)return;let t=this._mediaSource.duration,s=this._pendingMediaDuration;s>0&&(isNaN(t)||s>t)&&(NP_INFO(`Update MediaSource duration from ${t} to ${s}`),this._mediaSource.duration=s),this._requireSetMediaDuration=!1,this._pendingMediaDuration=0}_doRemoveRanges(){for(let e in this._pendingRemoveRanges){if(!this._sourceBuffers[e]||this._sourceBuffers[e].updating)continue;let t=this._sourceBuffers[e],s=this._pendingRemoveRanges[e];for(;s.length&&!t.updating;){let i=s.shift();t.remove(i.start,i.end)}}}_doAppendSegments(){let e=this._pendingSegments;for(let t in e)if(!(!this._sourceBuffers[t]||this._sourceBuffers[t].updating||this._mediaSource.streaming===!1)&&e[t].length>0){let s=e[t].shift();if(typeof s.timestampOffset=="number"&&isFinite(s.timestampOffset)){let i=this._sourceBuffers[t].timestampOffset,n=s.timestampOffset/1e3;Math.abs(i-n)>.1&&(NP_INFO(`Update MPEG audio timestampOffset from ${i} to ${n}`),this._sourceBuffers[t].timestampOffset=n),delete s.timestampOffset}if(!s.data||s.data.byteLength===0)continue;try{this._sourceBuffers[t].appendBuffer(s.data),this._isBufferFull=!1}catch(i){this._pendingSegments[t].unshift(s),i.code===22?(this._isBufferFull||this._emitter.emit(U.BUFFER_FULL),this._isBufferFull=!0):this._emitter.emit(U.ERROR,{code:i.code,msg:i.message})}}}_onSourceOpen(){if(NP_INFO("MediaSource onSourceOpen"),this._mediaSource.removeEventListener("sourceopen",this.e.onSourceOpen),this._pendingSourceBufferInit.length>0){let e=this._pendingSourceBufferInit;for(;e.length;){let t=e.shift();this.appendInitSegment(t,!0)}}this._hasPendingSegments()&&this._doAppendSegments(),this._emitter.emit(U.SOURCE_OPEN)}_onStartStreaming(){NP_INFO("ManagedMediaSource onStartStreaming"),this._emitter.emit(U.START_STREAMING)}_onEndStreaming(){NP_INFO("ManagedMediaSource onEndStreaming"),this._emitter.emit(U.END_STREAMING)}_onQualityChange(){NP_INFO("ManagedMediaSource onQualityChange")}_onSourceEnded(){NP_INFO("MediaSource onSourceEnded")}_onSourceClose(){NP_INFO("MediaSource onSourceClose"),this._mediaSource&&this.e!=null&&(this._mediaSource.removeEventListener("sourceopen",this.e.onSourceOpen),this._mediaSource.removeEventListener("sourceended",this.e.onSourceEnded),this._mediaSource.removeEventListener("sourceclose",this.e.onSourceClose),this._useManagedMediaSource&&(this._mediaSource.removeEventListener("startstraming",this.e.onStartStreaming),this._mediaSource.removeEventListener("endstreaming",this.e.onEndStreaming),this._mediaSource.removeEventListener("qualitychange",this.e.onQualityChange)))}_hasPendingSegments(){let e=this._pendingSegments;return e.video.length>0||e.audio.length>0}_hasPendingRemoveRanges(){let e=this._pendingRemoveRanges;return e.video.length>0||e.audio.length>0}_onSourceBufferUpdateEnd(){this._requireSetMediaDuration?this._updateMediaSourceDuration():this._hasPendingRemoveRanges()?this._doRemoveRanges():this._hasPendingSegments()?this._doAppendSegments():this._hasPendingEos&&this.endOfStream(),this._emitter.emit(U.UPDATE_END)}_onSourceBufferError(e){this._emitter.emit(U.ERROR,e)}}function ae(){let a=new OfflineAudioContext(1,1,44100);return!!(a.audioWorklet&&typeof a.audioWorklet.addModule=="function")}function oe(){class a extends AudioWorkletProcessor{constructor(){super(),this.state=0,this.start=!0,this.samplesArray0=[],this.samplesArray1=[],this.offset=0,this.bufferSize=1024,this.port.onmessage=t=>{t.data.message=="init"?this.bufferSize=t.data.bufferSize:t.data.message=="stop"?this.start=!1:t.data.message=="data"?(this.samplesArray0.push(t.data.buffer[0]),this.samplesArray1.push(t.data.buffer[1])):t.data.message=="zero"}}process(t,s,i){const n=s[0],o=n[0],l=n[1];if(this.offset==0&&this.port.postMessage({message:"beep"}),this.state==0&&this.samplesArray0.length>3&&this.samplesArray1.length>3&&(this.state=1),this.state==1){let w=this.samplesArray0[0],u=this.samplesArray1[0];for(let h=0;h<o.length;h++)o[h]=w[h+this.offset];for(let h=0;h<l.length;h++)l[h]=u[h+this.offset]}else o.fill(0),l.fill(0);return this.offset+=128,this.offset==this.bufferSize&&(this.offset=0,this.state==1&&(this.samplesArray0.shift(),this.samplesArray1.shift()),this.samplesArray0.length==0&&this.samplesArray1.length==0&&(this.state=0)),this.start}}registerProcessor("nm-wa-processor",a)}function le(a){return a.trim().match(/^function\s*\w*\s*\([\w\s,]*\)\s*{([\w\W]*?)}$/)[1]}function ue(){const a=le(oe.toString()),e=new Blob([a],{type:"text/javascript"});return URL.createObjectURL(e)}function he(a){const e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";let t="";for(let s=0;s<a;s++){const i=Math.floor(Math.random()*e.length);t+=e.charAt(i)}return t}L.WAB=ue,L.MP4=ne,L.MSE=re,L.AMF=D,Module.NodePlayer=L,window.NodePlayer=L})();
