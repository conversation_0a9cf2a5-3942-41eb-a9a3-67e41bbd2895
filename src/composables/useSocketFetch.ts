import {ref} from "vue";
import emitter, {type GameEvent} from "@/utils/emitter.ts";

const fetchMap = new Map();

const withResolvers = <T>() => {
  let resolve: any;
  let reject: any;
  const promise = new Promise<T>((res, rej) => {
    resolve = res;
    reject = rej;
  });
  return { promise, resolve, reject };
}

export const useSocketFetch = (reqEvent: keyof GameEvent, resEvent: keyof GameEvent, data?: any) => {
  let { promise, resolve, reject } = withResolvers<SocketData>();

  let timer: any;

  const timeout = 5000;

  const fetchId = ref<string>();

  const handleResponse = (res: SocketData) => {
    const { msgId, data } = res;

    if (!fetchMap.has(msgId)) return;

    emitter.off(resEvent, handleResponse);
    fetchMap.delete(msgId);
    timer && clearTimeout(timer);
    timer = undefined;

    if (data.code === 200) {
      resolve(res);
    } else {
      reject(msgId);
    }
  }

  // 接收数据
  emitter.on(resEvent, handleResponse);

  // 发送请求
  emitter.emit(reqEvent, {
    data: data,
    callback: (msgId: string) => {
      if (!msgId) {
        reject(`no msg id, reqEvent: ${reqEvent}`);
        return;
      }
      fetchId.value = msgId;
      fetchMap.set(msgId, promise);
    }
  });

  // 设置超时
  setTimeout(() => {
    emitter.off(resEvent, handleResponse);
    fetchMap.delete(fetchId.value);
    reject(fetchId.value);
  }, timeout);

  return { fetchId, promise };
}