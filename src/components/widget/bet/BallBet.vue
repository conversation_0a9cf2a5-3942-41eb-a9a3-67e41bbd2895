<script setup lang="ts">
import { onMounted, onUnmounted, ref, watchEffect } from "vue";
import { useI18n } from "vue-i18n";
import { storeToRefs } from "pinia";
import { list } from "radash";
import { useGameStore, usePersistStore, useUserStore } from "@/stores";
import { GamePlayType, GameStatus } from "@/constant/gameData.ts";
import { openMessage } from "@/components/ui/GMessage/index.ts";
import { formatNumber } from "@/utils/utils.ts";
import emitter from "@/utils/emitter.ts";
import {
  handleBet,
  handleUpdateLocalBalance,
  handleUpdateLocalBetInfo,
  playBetAnim,
  playBackBetAnim,
  playToBalanceAnim
} from "./betHelper";

import EffectWin from "@/components/widget/EffectWin.vue";

const {
  gameRoundId,
  gameId,
  gamePlayType,
  gameStatus,
  gameWinBet,
} = storeToRefs(useGameStore());
const { balance } = storeToRefs(useUserStore());
const { betAmountInfo, playerBetInfoMap } = storeToRefs(usePersistStore());
const { setPlayerBetTotal } = usePersistStore();

const { t } = useI18n();

const lists = ref<BetItemData[]>(list(0, 9, i => {
  const location = i + 1;
  return {
    type: GamePlayType.CHAMPION,
    location: String(location),
    amount: `${GamePlayType.CHAMPION}-${location}`,
    odds: "1:1",
    image: `/images/ball-${location}.png`,
    bets: 0,
    count: 0,
  };
}));

// 投放筹码
const takeBet = async (event: MouseEvent, index: number) => {
  if (gameStatus.value !== GameStatus.GAME_BET) {
    return openMessage({ message: t('no_bets_available_tips') });
  }

  const target = event.currentTarget as HTMLElement;

  const chip = document.querySelector(".chip-item.active") as HTMLElement;
  const chipInner = chip?.querySelector(".chip-item--inner") as HTMLElement;
  const targetEl = target.querySelector('.bet-item--chips') as HTMLElement;

  if (!chip || !chipInner || !targetEl) return console.error('not fount chip element');

  //获取下注金额
  const amount = Number(chip.dataset.value ?? 0);

  if(balance.value < amount) {
    openMessage({ message: t('balance_not_enough_tis') });
    emitter.emit("user-bet-balance-fail");
    return;
  }

  emitter.emit('play-sound', 'SOUND_5');

  playBetAnim(chipInner, targetEl, 30);

  try {
    const promise = handleBet({
      type: 1,
      betAmount: amount,
      gameId: gameId.value,
      roundId: gameRoundId.value,
      location: lists.value[index].location,
    });

    const res = await promise;

    setPlayerBetTotal(res.data?.data?.total);    
    handleUpdateLocalBalance(amount);
    handleUpdateLocalBetInfo({
      betAmount: amount,
      betLocation: lists.value[index].location,
      playTypeId: lists.value[index].type,
    });
  } catch (e) {
    console.error('bet error', e);
  }
};

// 撤销下注
const backBets = async () => {
  const chipInner: HTMLElement | null = document.querySelector(".chip-item.active .chip-item--inner");
  const betList: HTMLImageElement[] = Array.from(document.querySelectorAll('.ball-bets'));

  if (!chipInner || !betList.length) return;

  playBackBetAnim(betList, chipInner);
};

// 播放中奖后余额更新动画
const updateBetBalance = () => {
  const chips: HTMLImageElement[] = Array.from(document.querySelectorAll('.ball-bet-item.win .ball-bets'));
  const balance = document.querySelector('#balance-text') as HTMLElement;
  if (chips.length && balance) {
    playToBalanceAnim(chips, balance);
  }
};

const showWin = (location: number) => {
  return gameWinBet.value.type1Loc === location;
};

watchEffect(() => {
  lists.value.forEach(item => {
    const betItem = playerBetInfoMap.value[item.amount];
    if (betItem) {
      item.bets = betItem.betAmount;
      item.count = betItem.count;
    } else {
      item.bets = 0;
      item.count = 0;
    }
  });
});

watchEffect(() => {
  if (gamePlayType.value?.length) {
    const championInfo = gamePlayType.value.find((item: any) => item.playTypeId === GamePlayType.CHAMPION);
    if (championInfo) {
      lists.value.forEach((item) => {
        item.odds = `1:${championInfo.rate[0]}`;
      });
    }
  }
});

watchEffect(() => {
  if (gameStatus.value === GameStatus.GAME_SETTLEMENT) {
    setTimeout(updateBetBalance, 1600)
  }
});

// 组件挂载时
onMounted(() => {
  emitter.on("handle-revoke-bet", backBets);
});

// 组件卸载时
onUnmounted(() => {
  emitter.off("handle-revoke-bet", backBets);
});

</script>

<template>
  <div class="ball-bet-widget">
    <section
      v-for="(item, index) in lists"
      :key="index"
      class="ball-bet-item cursor-pointer"
      :class="{ win: showWin(Number(item.location)) }"
      @click="takeBet($event, index)"
    >
      <EffectWin v-if="showWin(Number(item.location))" />
      <div class="bet-item--rate">
        <img :src="item.image" :alt="`ball ${index}`" />
        <div>{{ item.odds }}</div>
      </div>
      <div class="bet-item--chips">
        <template v-if="item.count > 0">
          <img
            :src="`/images/bets-${Math.max(1, Math.min(item.count, 3))}.png`"
            :alt="`${index}-bets`"
            class="ball-bets block h-[16px]"
          />
        </template>
      </div>
      <div class="bet-item--bets">
        <span class="text-[#EFE70C]">{{ formatNumber(item.bets) }}</span>/{{ formatNumber(betAmountInfo[item.amount] ?? 0) }}
      </div>
    </section>
  </div>
</template>

<style lang="scss" scoped>
:where([data-layout="x"]) .ball-bet-widget {
  grid-template-columns: repeat(10, minmax(0, 1fr));
  padding: 4px;
  gap: 1px;
}

:where([data-layout="y"]) .ball-bet-widget {
  grid-template-columns: repeat(5, minmax(0, 1fr));
  padding: 6px 8px;
  gap: 6px 2px;
}

.ball-bet-widget {
  display: grid;
  background:
      linear-gradient(90deg, #001458 10.47%, #7c0013 100%) center/calc(100% - 4px) calc(100% - 4px) no-repeat,
      linear-gradient(90deg, #002394 10.47%, #aa002a 100%) center/100% no-repeat;
}

.ball-bet-widget .ball-bet-item {
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.3);
  text-align: center;
}

.ball-bet-widget .ball-bet-item::after {
  opacity: 0;
  pointer-events: none;
  position: absolute;
  inset: 0;
  margin: auto;
  width: 100%;
  height: 100%;
  transition: opacity 0.6s ease;
  transform: scale(1.046);
  content: "";
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  background-image: url("/images/bet-light-border2.png");
}

.ball-bet-widget .ball-bet-item:hover::after {
  transition: none;
  opacity: 1;
}

.ball-bet-widget .ball-bet-item .bet-item--rate {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  padding-top: 6px;
  padding-bottom: 4px;
}

.ball-bet-widget .ball-bet-item .bet-item--rate img {
  display: block;
  width: 24px;
  height: 24px;
}

.ball-bet-widget .ball-bet-item .bet-item--rate div {
  font-size: 12px;
  font-weight: 700;
  color: white;
}

.ball-bet-widget .ball-bet-item .bet-item--chips {
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.ball-bet-widget .ball-bet-item .bet-item--bets {
  padding-block: 4px;
  border-top: 1px solid rgba(255, 255, 255, 0.3);
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  text-overflow: ellipsis;
  overflow: hidden;
}
</style>
