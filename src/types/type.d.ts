// =============================================================================
// Socket Communication Types
// =============================================================================

/**
 * Socket response data structure
 */
declare interface SocketResData {
  /** Unique message identifier */
  messageId: string;
  /** Response data payload */
  data: string;
  /** API endpoint path */
  path: string;
}

/**
 * Socket data structure for incoming messages
 */
declare interface SocketData {
  /** Timestamp when message was created */
  currentTimeMillis: number;
  /** Message payload data */
  data: any;
  /** Message identifier */
  msgId: string;
  /** API endpoint path */
  path: string;
  /** Source server name */
  serverName: string;
}

/**
 * Generic emitter callback data structure
 */
declare interface EmitterCallbackData<T, K> {
  /** Optional data payload */
  data?: T;
  /** Optional callback function */
  callback?: (data: K) => void;
}

/**
 * Message handler function type
 */
declare type MessageHandler = (data: SocketData) => void;

// =============================================================================
// Game Betting Types
// =============================================================================

/**
 * Betting item response from server
 */
declare interface BetItemRes {
  /** Amount of the bet */
  betAmount: number;
  /** Location where bet was placed */
  betLocation: string;
  /** Number of bets */
  count: number;
  /** Currency type */
  currency: string;
  /** Play type identifier */
  playTypeId: string;
  /** Win/lose status (optional) */
  winOrLose?: number;
  /** Award amount if won (optional) */
  awardAmount?: number;
}

/**
 * Betting item data for client
 */
declare interface BetItemData {
  /** Game play type */
  type: import('@/constant/gameData').GamePlayType;
  /** Bet location */
  location: string;
  /** Bet amount as string */
  amount: string;
  /** Betting odds */
  odds: string;
  /** Number of bets placed */
  bets: number;
  /** Total count */
  count: number;
  /** Optional display title */
  title?: string;
  /** Optional image URL */
  image?: string;
}

/**
 * Betting chips result
 */
declare interface BetChipsResult {
  /** Current bet list */
  betList?: string;
  /** Original bet list */
  originBetList?: string;
}

/**
 * Win location mapping
 */
declare interface WinLocation {
  /** Type 1 location */
  type1Loc?: number;
  /** Type 2 location */
  type2Loc?: number;
  /** Type 3 location */
  type3Loc?: number;
  /** Type 4 location */
  type4Loc?: number;
}

/**
 * Game type configuration data
 */
declare interface GameTypeData {
  /** Betting title */
  betTitle: any;
  /** Play type identifier */
  playTypeId: string;
  /** Play type display name */
  playTypeName: string;
  /** Rate configuration */
  rate: any;
  /** Sort order */
  sort: number;
  /** Active status */
  status: boolean;
}

/**
 * Historical trend data
 */
declare interface HistoryTrend {
  /** Round identifier */
  roundId: string;
  /** Game result */
  result: string;
  /** Whether result is even */
  even: boolean;
  /** Result number as string */
  num: string;
}

// =============================================================================
// Video Stream Types
// =============================================================================

/**
 * Video stream configuration
 */
declare interface VideoStream {
  /** Stream title */
  title: string;
  /** Stream URL */
  url: string;
  /** Video resolution */
  resolution: number;
}

/**
 * Video block with multiple stream options
 */
declare interface VideoBlock {
  [key in number]: {
    /** Stream option A */
    a?: VideoStream;
    /** Stream option B */
    b?: VideoStream;
  };
}

/**
 * Complete video stream data structure
 */
declare interface VideoStreamData {
  /** Domestic video streams */
  domestic: VideoBlock;
  /** International video streams */
  international: VideoBlock;
}

// =============================================================================
// Audio System Types
// =============================================================================

/**
 * Audio configuration for sound management
 */
declare interface SoundConfig {
  /** 音频的唯一标识符，用于在音频管理器中引用特定的音频 */
  id: string;
  /** 音频文件的路径或URL（例如 'background_music.mp3'），支持MP3、OGG等格式 */
  src: string;
  /** 是否循环播放音频，默认为 false。常用于背景音乐（true）或一次性音效（false） */
  loop?: boolean;
  /** 音频的初始音量，范围 0.0 到 1.0，默认为 1.0。用于设置默认播放音量 */
  volume?: number;
  /** 音频池大小，指定并发播放的音频实例数，默认为 1。适用于频繁触发的音效（如跳跃音效） */
  poolSize?: number;
}