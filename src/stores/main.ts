import { computed, reactive, ref } from "vue";
import { defineStore } from 'pinia';
import { SYSTEM_ERROR_CODE } from "@/constant";
import { CameraType } from "@/constant/gameData";
import { languages } from "@/i18n";

type CameraInfo = {
  type: CameraType,
  quality: number;
}

export const useMainStore = defineStore('main', () => {
  const systemError = ref<SYSTEM_ERROR_CODE>(SYSTEM_ERROR_CODE.NONE);
  const isSystemError = computed(() => {
    return systemError.value !== SYSTEM_ERROR_CODE.NONE;
  })
  const setSystemError = (code: SYSTEM_ERROR_CODE) => {
    systemError.value = code;
  }

  const scale = ref<number>(1);
  const setScale = (newScale: number) => {
    scale.value = newScale;
  }

  // 设备当前方向
  const orientation = ref<'landscape' | 'portrait'>('portrait');
  const setOrientation = (newOrientation: 'landscape' | 'portrait') => {
    orientation.value = newOrientation;
  }

  // 只有移动端才有是否旋转（表示在移动端手动横屏）
  const isRotation = ref<boolean>(false);
  const toggleIsRotation = (status?: boolean) => {
    isRotation.value = status ?? !isRotation.value;
  }

  const mainVolume = ref(1);
  const setMainVolume = (newVolume: number) => {
    mainVolume.value = newVolume;
  }

  const soundVolume = ref(1);
  const setSoundVolume = (newVolume: number) => {
    soundVolume.value = newVolume;
  }

  const bmgVolume = ref(1);
  const setBmgVolume = (newVolume: number) => {
    bmgVolume.value = newVolume;
  }

  const layout = ref<'x' | 'y'>('x');
  const isVertical = computed(() => layout.value === 'y');
  const isHorizontal = computed(() => layout.value === 'x');
  const setLayout = (newLayout: 'x' | 'y') => {
    layout.value = newLayout;
  }

  const langs = ref(languages);
  const lang = ref('en');
  const restrictLang = () => {
    // 做一下判断，如果设置的语言不再支持的语言列表中，回退为第一个
    if (langs.value.find(item => item.value === lang.value)) {
      return;
    }
    lang.value = langs.value[0].value;
  };
  const setLangs = (newLangs: string[]) => {
    if (!newLangs || !newLangs.length) {
      newLangs = [languages[0].value];
    }
    langs.value = languages.filter(item => newLangs.includes(item.value));
    restrictLang();
  }
  const setLang = (newLang: string) => {
    lang.value = newLang;
    restrictLang();
  }

  const isShowXBetPanel = ref<boolean>(false);
  const toggleXBetPanelShow = (status?: boolean) => {
    isShowXBetPanel.value = status ?? !isShowXBetPanel.value;
  }

  const isShowXHistory = ref<boolean>(false);
  const toggleXHistoryShow = (show?: boolean) => {
    isShowXHistory.value = show ?? !isShowXHistory.value;
  }

  const isShowVideoLoading = ref<boolean>(false);
  const toggleVideoLoading = (show?: boolean) => {
    isShowVideoLoading.value = show ?? !isShowVideoLoading.value;
  }

  const cameraInfo = reactive<CameraInfo>({
    type: CameraType.A,
    quality: 480,
  });

  const toggleCameraType = (type?: CameraType) => {
    if (type) {
      cameraInfo.type = type;
      return;
    }
    if (cameraInfo.type === CameraType.A) {
      cameraInfo.type = CameraType.B;
      return;
    }
    if (cameraInfo.type === CameraType.B) {
      cameraInfo.type = CameraType.A;
      return;
    }
  }

  const setCameraQuality = (quality: number) => {
    cameraInfo.quality = quality;
  }

  return {
    systemError, isSystemError, setSystemError,
    scale, setScale,
    orientation, setOrientation,
    isRotation, toggleIsRotation,
    mainVolume, setMainVolume,
    bmgVolume, setBmgVolume,
    soundVolume, setSoundVolume,
    layout, isVertical, isHorizontal, setLayout,
    isShowXBetPanel, toggleXBetPanelShow,
    langs, setLangs, lang, setLang,
    isShowXHistory, toggleXHistoryShow,
    isShowVideoLoading, toggleVideoLoading,
    cameraInfo, toggleCameraType, setCameraQuality,
  };
})