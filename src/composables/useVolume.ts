import {nextTick, onMounted, ref, watch} from "vue";
import {storeToRefs} from "pinia";
import {random} from "radash";
import {useGameStore, useMainStore} from "@/stores";
import {GameStatus, sounds} from "@/constant/gameData.ts";
import AudioManager from "@/utils/audioManager.ts";

export function useVolume() {
  const isInit = ref<boolean>(false);
  const { gameStatus } = storeToRefs(useGameStore());
  const { mainVolume, bmgVolume, soundVolume } = storeToRefs(useMainStore());
  const audioManager = new AudioManager({
    mainVolume: mainVolume.value,
    bgmVolume: bmgVolume.value,
    soundVolume: soundVolume.value
  });

  audioManager.preload(sounds);

  const switchLoopMusic = (key: string) => {
    const loopMusicList = ["BGM_VIDEO1", "BGM_VIDEO2", "BGM_VIDEO3", "BGM_REWARD", "BGM_WAIT_BET"];
    loopMusicList.forEach(item => {
      if (key === item) {
        audioManager.play(item);
      } else {
        audioManager.pause(item);
      }
    })
  }

  const playMusic = () => {
    switch (gameStatus.value) {
      case GameStatus.GAME_BET:
        return switchLoopMusic('BGM_WAIT_BET');
      case GameStatus.GAME_START:
        return switchLoopMusic(`BGM_VIDEO${random(1,3)}`);
      case GameStatus.GAME_SETTLEMENT:
        return switchLoopMusic('BGM_REWARD');
    }
  }

  watch(gameStatus, playMusic);

  watch(mainVolume, (newVolume) => {
    audioManager.setVolume('main', newVolume);
  });
  watch(bmgVolume, (newVolume) => {
    audioManager.setVolume('bgm', newVolume);
  });
  watch(soundVolume, (newVolume) => {
    audioManager.setVolume('sound', newVolume);
  });

  const initAudioManager = (): void => {
    audioManager.init();
    isInit.value = true;
  };

  const playSound = (key: string): void => {
    audioManager.play(key);
  };

  onMounted(() => {
    const handleInteraction = async () => {
      initAudioManager();
      await nextTick();
      playMusic();
      // 移除监听器，避免重复触发
      document.removeEventListener('click', handleInteraction);
      document.removeEventListener('touchstart', handleInteraction);
    };

    // 添加全局 click 和 touchstart 监听
    document.addEventListener('click', handleInteraction, { once: true });
    document.addEventListener('touchstart', handleInteraction, { once: true });
  });

  return { playSound }
}