<script setup lang="ts">
import {onMounted, ref, watch} from "vue";
import {storeToRefs} from "pinia";
import {animate} from "animejs";
import {useGameStore, useMainStore} from "@/stores";
import {GameStatus} from "@/constant/gameData.ts";

import CurrentPeriod from "@/components/widget/CurrentPeriod.vue";
import BetAmount from "@/components/widget/BetAmount.vue";
import DragonTigerBet from "@/components/widget/bet/DragonTigerBet.vue";
import BallBet from "@/components/widget/bet/BallBet.vue";
import BetBar from "@/components/widget/bet/BetBar.vue";
import emitter from "@/utils/emitter";

const { gameStatus } = storeToRefs(useGameStore());
const { isShowXBetPanel } = storeToRefs(useMainStore());
const { toggleXBetPanelShow } = useMainStore();

const showPanelStatus: GameStatus[] = [
  GameStatus.GAME_WAIT,
  GameStatus.GAME_BET,
  GameStatus.GAME_END,
  GameStatus.GAME_SETTLEMENT,
]

const betBoxRef = ref();
const betBoxInnerRef = ref();

const updateBetPanelShow = () => {
  if (showPanelStatus.includes(gameStatus.value!)) {
    toggleXBetPanelShow(true);
  } else {
    toggleXBetPanelShow(false);
  }
}


onMounted(() => {
  updateBetPanelShow();

  watch(gameStatus, () => {
    updateBetPanelShow();
  });

  watch(isShowXBetPanel, (show) => {
    animate(betBoxRef.value, {
      height: show ? betBoxInnerRef.value?.clientHeight : 0,
      duration: 600,
      ease: 'inOut',
      onComplete: self => {
        self.cancel();
        emitter.emit('bet-panel-switched');
      }
    });
  }, { immediate: true });
});
</script>

<template>
  <div class="relative bg-black/50">
    <div class="absolute bottom-full left-0 translate-y-[0.1px]">
      <CurrentPeriod />
    </div>

    <div class="absolute bottom-full inset-x-0 flex justify-center translate-y-[0.1px]">
      <BetAmount />
    </div>

    <div ref="betBoxRef" class="overflow-hidden h-0">
      <div ref="betBoxInnerRef" class="flex flex-col gap-1 p-1">
        <DragonTigerBet />
        <BallBet />
      </div>
    </div>

    <BetBar />
  </div>
</template>

<style scoped>

</style>