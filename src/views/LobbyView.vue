<script setup lang="ts">
import {onMounted, onUnmounted, ref, watchEffect} from "vue";
import {storeToRefs} from "pinia";
import {useGameStore, useMainStore, usePersistStore, useUserStore} from "@/stores";
import {useWebSocket} from "@/composables/useWebSocket.ts";
import {useNoResponse} from "@/composables/useNoResponse";
import {useDisableZoom} from "@/composables/useDisableZoom.ts";
import {useVolume} from "@/composables/useVolume.ts";
import {SYSTEM_ERROR_CODE} from "@/constant";
import {SocketEventCode} from "@/constant/gameData.ts";
import {closeFullscreen, glog, openFullscreen} from "@/utils/utils";
import emitter from "@/utils/emitter.ts";

import MaintainInfo from "@/components/MaintainInfo/index.vue";
import InitScene from "@/components/InitScene/index.vue";
import CanvasLayout from "@/layout/CanvasLayout.vue";
import MobileLayout from "@/layout/MobileLayout.vue";
import DesktopLayout from "@/layout/DesktopLayout.vue";

const { token } = storeToRefs(useUserStore());
const { setBalance, setCurrency, setCurrencySymbol, setUserId } = useUserStore();
const { isHorizontal, isSystemError } = storeToRefs(useMainStore());
const { setLangs, setSystemError } = useMainStore();
const { setPlayerBetTotal, setPlayerBetInfoMap, setBetAmountInfo } = usePersistStore();
const { isSocketOnline } = storeToRefs(useGameStore());
const {
  setIsSocketOnline,
  updateGameInfo,
  setVideoStreamList,
  setBetChipsList,
  setGamePlayType,
  setGameAwardAmount,
} = useGameStore();

const loading = ref(true);

const { reset } = useNoResponse(() => {
  // 如果socket是连接状态并且没有系统维护或网络错误 触发掉线
  if (isSocketOnline.value && !isSystemError.value) {
    glog('socket-no-response');
    emitter.emit('socket-no-response');
    setSystemError(SYSTEM_ERROR_CODE.NETWORK_ERROR);
  }
});

const setupSocketEvent = () => {
  if (!token.value) {
    setSystemError(SYSTEM_ERROR_CODE.SYSTEM_MAINTAIN);
    return;
  }
  const { connect, subscribe, send, disconnect, isConnected } = useWebSocket(
    `${import.meta.env.VITE_SOCKET_URL}/websocket?token=${token.value}`,
    () => {
      emitter.emit('get-game-config-req');
      emitter.emit('get-user-balance-req');
    }
  );
  // 订阅socket事件 通过emitter分发出去
  subscribe(SocketEventCode.SOCKET_INIT, data => emitter.emit('socket-init', data));
  subscribe(SocketEventCode.WAIT, data => emitter.emit('game-wait', data));
  subscribe(SocketEventCode.BET, data => emitter.emit('game-bet', data));
  subscribe(SocketEventCode.BET_END, data => emitter.emit('game-bet-end', data));
  subscribe(SocketEventCode.GAME_START, data => emitter.emit('game-start', data));
  subscribe(SocketEventCode.GAME_START_COUNTDOWN, data => emitter.emit('game-start-countdown', data));
  subscribe(SocketEventCode.SETTLEMENT, data => emitter.emit('game-settlement', data));
  subscribe(SocketEventCode.NOTICE_ONE_PRIZE, data => emitter.emit('game-award', data));
  subscribe(SocketEventCode.BET_AMOUNT, data => emitter.emit('game-bet-amount', data));

  subscribe(SocketEventCode.GAME_PAUSED, data => emitter.emit('game-paused', data));
  subscribe(SocketEventCode.KICK_OUT, data => emitter.emit('kick-out', data));
  subscribe(SocketEventCode.USER_BET, data => emitter.emit('user-bet-res', data));
  subscribe(SocketEventCode.USER_REVOKE, data => emitter.emit('user-revoke-res', data));
  subscribe(SocketEventCode.GET_GAME_CONFIG, data => emitter.emit('get-game-config-res', data));
  subscribe(SocketEventCode.GET_USER_BALANCE, data => emitter.emit('get-user-balance-res', data));
  subscribe(SocketEventCode.GET_GAME_HISTORY, data => emitter.emit('get-game-history-res', data));
  subscribe(SocketEventCode.GET_GAME_HISTORY_BET, data => emitter.emit('get-game-history-bet-res', data));
  subscribe(SocketEventCode.GET_PLAYER_BET, data => emitter.emit('get-player-bet-res', data));
  subscribe(SocketEventCode.GET_GAME_RULES, data => emitter.emit('get-game-rules-res', data));

  // 游戏配置请求
  emitter.on('get-game-config-req', () => {
    send(SocketEventCode.GET_GAME_CONFIG);
  });
  // 游戏配置返回
  emitter.on('get-game-config-res', ({ data: res }: SocketData) => {
    const { code, data } = res || {};
    if (code === 200) {
      setLangs(data.languageList);
      setVideoStreamList(data.playUrl);
      setBetChipsList(data.gameBetConfig);
      setGamePlayType(data.typeList);
    }
  });

  // 用户余额请求
  emitter.on('get-user-balance-req', () => {
    send(SocketEventCode.GET_USER_BALANCE);
  });
  // 用户余额返回
  emitter.on('get-user-balance-res', ({ data: res }: SocketData) => {
    const { code, data } = res || {};
    if (code === 200) {
      setBalance(data?.balance);
      setCurrency(data?.currency);
      setCurrencySymbol(data?.icon);
      setUserId(data?.userId);
    }
  });

  // 获取当前玩家投注结果请求
  emitter.on('get-player-bet-req', (res: { roundId: string; gameId: string }) => {
    send(SocketEventCode.GET_PLAYER_BET, res);
  });
  // 获取当前玩家投注结果返回
  emitter.on("get-player-bet-res", (res: SocketData) => {
    const { data, code } = res.data || {};
    if (code === 200) {
      setPlayerBetTotal(data?.totalBet ?? 0);
      setPlayerBetInfoMap(data?.recordLists ?? []);
    }
  });

  // 获取游戏规则请求
  emitter.on('get-game-rules-req', (data: EmitterCallbackData<{ language: string }, string>) => {
    const msgId = send(SocketEventCode.GET_GAME_RULES, data.data) ?? '';
    data.callback?.(msgId);
  });

  //获取游戏历史下注记录请求
  emitter.on('get-game-history-bet-req', (data: EmitterCallbackData<any, string>) => {
    const msgId = send(SocketEventCode.GET_GAME_HISTORY_BET) ?? '';
    data.callback?.(msgId);
  });

  //获取游戏历史结果请求
  emitter.on('get-game-history-req', (data: EmitterCallbackData<any, string>) => {
    const msgId = send(SocketEventCode.GET_GAME_HISTORY) ?? '';
    data.callback?.(msgId);
  });

  // 投注请求
  emitter.on('user-bet-req', (data: EmitterCallbackData<any, string>) => {
    const msgId = send(SocketEventCode.USER_BET, data.data) ?? '';
    data.callback?.(msgId);
  });

  // 撤销请求
  emitter.on('user-revoke-req', (data: EmitterCallbackData<any, string>) => {
    const msgId = send(SocketEventCode.USER_REVOKE, data.data) ?? '';
    data.callback?.(msgId);
  });

  // socket首次进入返回当前游戏状态
  emitter.on('socket-init', (res: SocketData) => {
    const { data } = res?.data || {};

    const status = data?.state?.toString();

    switch (status) {
      case SocketEventCode.GAME_PAUSED:
        emitter.emit('game-paused', { ...res, path: status });
        break;
      case SocketEventCode.WAIT:
        emitter.emit('game-wait', { ...res, path: status });
        break;
      case SocketEventCode.BET:
        emitter.emit('game-bet', { ...res, path: status });
        break;
      case SocketEventCode.BET_END:
        emitter.emit('game-bet-end', { ...res, path: status });
        break;
      case SocketEventCode.GAME_START:
        emitter.emit('game-start', { ...res, path: status });
        break;
      case SocketEventCode.GAME_START_COUNTDOWN:
        emitter.emit('game-start-countdown', { ...res, path: status });
        break;
      // case SocketEventCode.GAME_END:
      //   emitter.emit('game-end', { ...res, path: status });
      //   break;
      case SocketEventCode.SETTLEMENT:
        emitter.emit('game-settlement', { ...res, path: status });
        break;
    }

    emitter.emit('get-player-bet-req', {
      roundId: data?.roundId,
      gameId: data?.gameId,
    });
  });
  // 等待游戏开始投注
  emitter.on('game-wait', (res: SocketData) => {
    reset();
    setSystemError(SYSTEM_ERROR_CODE.NONE);
    updateGameInfo(res);
    setGameAwardAmount(0);
    setPlayerBetTotal(0);
    setPlayerBetInfoMap([]);
    setBetAmountInfo({});
    emitter.emit('play-sound', 'SOUND_6');
    emitter.emit('get-user-balance-req');
    emitter.emit('show-switch-scenes');
  });
  // 游戏开始下注
  emitter.on('game-bet', (res: SocketData) => {
    reset();
    updateGameInfo(res);
  });
  // 游戏下注结束
  emitter.on('game-bet-end', (res: SocketData) => {
    reset();
    updateGameInfo(res);
    emitter.emit('show-switch-scenes');
  });
  // 游戏开始
  emitter.on('game-start', (res: SocketData) => {
    reset();
    updateGameInfo(res);
  });
  // 游戏开始倒计时
  emitter.on('game-start-countdown', () => {
    reset();
    // 延迟7s对准直播流倒计时画面
    setTimeout(() => emitter.emit('play-sound', 'SOUND_14'), 7000);
  });
  // 游戏结算
  emitter.on('game-settlement', (res: SocketData) => {
    reset();
    updateGameInfo(res);
    emitter.emit('play-sound', 'SOUND_13');
  });
  // 中奖推送
  emitter.on('game-award', (res: SocketData) => {
    reset();
    updateGameInfo(res);
  });

  // 游戏维护
  emitter.on('game-paused', (res: SocketData) => {
    if (res.data?.code === 200) {
      setSystemError(SYSTEM_ERROR_CODE.SYSTEM_MAINTAIN);
    }
  });
  // 被挤下线
  emitter.on('kick-out', () => {
    console.log("kick-out............")
    disconnect();
  });
  // socket假死
  emitter.on('socket-no-response', () => {
    disconnect();
  });

  connect();

  watchEffect(() => setIsSocketOnline(isConnected.value));
}

const setupOtherEvent = () => {
  emitter.on('socket-reconnect-fail', () => {
    setSystemError(SYSTEM_ERROR_CODE.NETWORK_ERROR);
  });
}

const setupVolume = () => {
  const { playSound } = useVolume();

  // 音效事件监听
  emitter.on('play-sound', (key: string) => playSound(key));
}

const handleFinish = () => {
  loading.value = false;
}

useDisableZoom();

setupVolume();

setupSocketEvent();
setupOtherEvent();

const toggoleFullscreen = () => {
  if (document.fullscreenElement) {
    closeFullscreen()
  } else {
    openFullscreen();
  }
}

onMounted(() => {
  // document.addEventListener('click', toggoleFullscreen);
});

onUnmounted(() => {
  // document.removeEventListener('click', toggoleFullscreen);
});
</script>

<template>
  <CanvasLayout>
    <div id="view-box">
      <MaintainInfo v-if="isSystemError" />

      <InitScene v-if="loading" @finish="handleFinish" />

      <Component v-else :is="isHorizontal ? DesktopLayout : MobileLayout" />
    </div>
  </CanvasLayout>
</template>

<style scoped>
#view-box {
  width: 100%;
  height: 100%;
  position: relative;
  overscroll-behavior: none;
  overflow: hidden;
}
</style>