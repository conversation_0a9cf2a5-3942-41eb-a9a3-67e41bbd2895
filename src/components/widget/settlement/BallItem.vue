<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { useMainStore } from '@/stores';

const props = defineProps<{
  ball: string; // 当前球号码
  order: number; // 当前球排名
}>();

const { isHorizontal, isVertical } = storeToRefs(useMainStore());
</script>

<template>
  <div class="relative flex flex-col items-center">
    <img :src="`/images/ball-${props.ball}.png`" :alt="`ball ${props.ball}`" :class="{ 'w-[24.5px]': isVertical }" class="w-4"/>
    <img
      src="/images/ball-bg.png"
      alt="ball order bg"
      :class="{
        'w-[22px] mt-[-9px]': isHorizontal,
        'w-[28px] mt-[-11px]': isVertical
      }"
    />
    <span
      :class="{
        'top-[15px]': isHorizontal,
        'top-[27px]': isVertical
      }"
      class="ball-order mt-1 text-white text-default"
    >
      {{ props.order }}
    </span>
  </div>
</template>

<style scoped>
.ball-order {
  position: absolute;
  font-weight: 500;
}
</style>