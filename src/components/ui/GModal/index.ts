import {type Component, createApp, h} from "vue";
import i18n from '@/i18n';
import type { ModalOptions } from "./types";

import GModal from '@/components/ui/GModal/index.vue';
import emitter from "@/utils/emitter.ts";

export function openModal (component: Component, props = {}) {
  emitter.emit('play-sound', 'SOUND_3');

  const modalContainer = document.createElement('div');
  const root = document.querySelector('#view-box');

  if (!root) return console.error('not fount container');

  root.appendChild(modalContainer);

  const modal = createApp({
    render() {
      return h(component, { ...props, onClose: close })
    },
  });

  modal.use(i18n);

  const close = () => {
    modal.unmount();
    modalContainer.remove();
  }

  modal.mount(modalContainer);

  return { close }
}

export function openModalWithGModal (component: Component, modalOptions?: ModalOptions, props = {}) {
  const modalContainer = document.createElement('div');
  const root = document.querySelector('#view-box');

  if (!root) return console.error('not fount container');

  root.appendChild(modalContainer);

  const modal = createApp({
    render() {
      return h(GModal, {
        ...modalOptions,
        show: true,
        onClose: close,
      }, {
        default: () => h(component, { ...props, onClose: close }),
      })
    },
  });

  modal.use(i18n);

  const close = () => {
    modal.unmount();
    modalContainer.remove();
  }

  modal.mount(modalContainer);

  return { close }
}