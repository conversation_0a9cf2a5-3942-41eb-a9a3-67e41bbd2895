<template>
  <div class="g-message" ref="messageRef" @click="handleClose">
    <div class="g-message-inner" @click.stop>
      <div class="message-icon">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
        >
          <path
            d="M10 19.1416C4.97206 19.1416 0.858399 15.0279 0.858399 10C0.8584 4.97204 4.97206 0.858397 10 0.858398C15.0279 0.858398 19.1416 4.97205 19.1416 10C19.1416 15.0279 15.0279 19.1416 10 19.1416ZM10 17.7334C14.3096 17.7334 17.7334 14.3095 17.7334 10C17.7334 5.69045 14.3096 2.2666 10 2.2666C5.69045 2.2666 2.2666 5.69045 2.2666 10C2.2666 14.3095 5.69045 17.7334 10 17.7334Z"
            fill="#FF003F"
            stroke="#FF003F"
            stroke-width="0.282298"
          />
          <circle
            cx="9.94716"
            cy="13.3163"
            r="0.705746"
            transform="rotate(-180 9.94716 13.3163)"
            fill="#FF003F"
            stroke="#FF003F"
            stroke-width="0.483245"
          />
          <rect
            x="10.4208"
            y="10.9482"
            width="0.947369"
            height="4.73684"
            rx="0.473684"
            transform="rotate(-180 10.4208 10.9482)"
            fill="#FF003F"
            stroke="#FF003F"
            stroke-width="0.947369"
          />
        </svg>
      </div>
      <div class="message-content">
        <span v-if="typeof props.message === 'string'">{{ props.message }}</span>
        <component v-else :is="props.message"/>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {onMounted, onUnmounted, useTemplateRef} from "vue";
import type {MessageOptions} from "@/components/ui/GMessage/types.ts";

const props = withDefaults(defineProps<MessageOptions>(), {
  type: 'warning',
  duration: 2000,
});

const emits = defineEmits<{
  (e: 'close'): void
}>()

let timer: any = null;

const messageRef = useTemplateRef('messageRef');

const handleClose = () => {
  timer && clearTimeout(timer);
  messageRef.value?.classList.add('hide');
  messageRef.value?.addEventListener('animationend', () => {
    emits('close');
  });
}

onMounted(() => {
  timer = setTimeout(handleClose, props.duration);
});

onUnmounted(() => {
  timer && clearTimeout(timer);
});
</script>

<style>
 .g-message {
   animation: modal-mask-show 0.3s ease-out forwards;
 }

.g-message-inner {
  animation: modal-inner-show 0.3s ease-out forwards;
}

.g-message.hide {
  animation: modal-mask-hide 0.3s ease-out forwards;
}

.g-message.hide .g-message-inner {
  animation: modal-inner-hide 0.3s ease-out forwards;
}

.g-message {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.g-message-inner {
  max-width: 279px;
  border: 1px solid #2d2c30;
  background: #242328;
  padding: 16px 58px;
}

.message-icon {
  display: flex;
  justify-content: center;
  padding: 0 0 12px 0;
  line-height: 0;
}

.message-content {
  color: #fff;
  text-align: center;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}
</style>
