<script setup lang="ts">
import {nextTick, onMounted, onUnmounted, ref, watchEffect} from "vue";
import {storeToRefs} from "pinia";
import {animate, createScope, createTimeline, JSAnimation, Timeline, utils} from "animejs";
import {useGameStore, useMainStore} from "@/stores";
import {GameStatus} from "@/constant/gameData";
import emitter from "@/utils/emitter.ts";

createScope({ root: '.countdown-box' });

const { isHorizontal, isVertical } = storeToRefs(useMainStore());
const { gameStatus } = storeToRefs(useGameStore());

const showing = ref(false);

let cancelFn: (() => void)[] = [];

// 背景dot运动动画
const setupBgAnim = () => {
  let tl: Timeline;

  const getRunPot = () => ({
    translateX: utils.random(-8, 8, 0),
    translateY: utils.random(-8, 8, 0),
    duration: utils.random(1000, 2000, 0),
    ease: 'easeInOutQuad',
  });

  const updateBg = () => {
    tl?.cancel();
    tl = createTimeline({
      duration: 1000,
      onComplete: updateBg,
    });
    tl.add('.dot-1', getRunPot());
    tl.add('.dot-2', getRunPot(), '<<');
    tl.add('.dot-3', getRunPot(), '<<');
    tl.add('.dot-4', getRunPot(), '<<');
    tl.add('.dot-5', getRunPot(), '<<');
    tl.add('.dot-6', getRunPot(), '<<');
  }

  updateBg();

  return () => tl?.cancel();
}

// 倒计时数字动画
const setupNumberAnim = () => {
  let anim1: JSAnimation;
  let anim2: JSAnimation;
  let timer: any;

  const numbers = (n: number) => {
    let currN = n < 1 ? 3 : n;
    let nextN = n === 1 ? 3 : n - 1;
    return [currN, nextN];
  }

  const getParams = (show: boolean) => {
    return {
      opacity: show ? { from : 0, to: 1 } : { from: 1, to: 0 },
      translateY: show ? { from: -30, to: 0 } : { from: 0, to: 30 },
      duration: 300,
      delay: 700,
      ease: 'inQuint',
    }
  }

  const updateNumber = async (n: number) => {
    const [n1, n2] = numbers(n);
    anim1?.cancel();
    anim2?.cancel();
    if (timer) {
      clearTimeout(timer);
      timer = undefined;
    }
    anim1 = animate(`.text-${n1}`, getParams(false));
    anim2 = animate(`.text-${n2}`, getParams(true));
    timer = setTimeout(() => updateNumber(n2), 1000);
  }

  updateNumber(3);

  return () => {
    if (timer) {
      clearTimeout(timer);
      timer = undefined;
    }
    anim1?.cancel();
    anim2?.cancel();
  }
}

// 背景圆环和方块的旋转动画
const setupRingAnim = () => {
  const anim1 = animate('.circle-ring', {
    rotate: ['0deg', '360deg'],
    scale: [1, 1.25, 1],
    loop: true,
    duration: 2000,
    ease: 'linear'
  });

  const anim2 = animate('.rect-ring', {
    rotate: '-180deg',
    loop: true,
    duration: 1000,
  });

  return () => {
    anim1?.cancel();
    anim1?.revert();
    anim2?.cancel();
    anim2?.revert();
  }
}

const setupShowHideAnim = (show: boolean, cb: () => void) => {
  animate('.countdown-box', {
    opacity: show ? 1 : 0,
    scale: show ? 1 : 0.3,
    duration: 300,
    ease: 'inOutBack(1.70158)',
    onComplete: self => {
      self.cancel();
      cb();
    },
  })
}

// 设置默认样式
const setDefaultStyle = () => {
  const defaultStyle = { opacity: 0, translateY: -30 };
  utils.set('.countdown-box', { opacity: 0, scale: 0.3 });
  utils.set('.text-1', defaultStyle);
  utils.set('.text-2', defaultStyle);
}

const show = () => {
  showing.value = true;
  // 清除动画 防止有可能会出现的重复调用show
  cancelFn.forEach(cb => cb?.());
  // 执行展示动画
  nextTick(() => {
    setDefaultStyle();
    setupShowHideAnim(true, () => {
      cancelFn = [
        setupBgAnim(),
        setupRingAnim(),
        setupNumberAnim(),
      ];
    });
  });
}

const hide = () => {
  // 防止没有触发shou，直接触发hide
  if (!showing.value) return;
  // 清除动画
  cancelFn.forEach(cb => cb?.());
  // 执行消失动画
  setupShowHideAnim(false, () => {
    showing.value = false;
  })
}

onMounted(() => {
  watchEffect(() => {
    if (gameStatus.value !== GameStatus.GAME_BET) {
      hide();
    }
  });
  emitter.on('play-bet-prompt', show);
});

onUnmounted(() => {
  cancelFn.forEach(cb => cb?.());
  emitter.off('play-bet-prompt', show);
});

</script>

<template>
  <div>
    <div v-if="showing" class="countdown-box absolute inset-0 m-auto z-10 pointer-events-none">
      <svg :class="{ '-translate-y-5': isHorizontal, '-translate-y-2.5': isVertical }" class="block size-full" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 222 222" fill="none">
        <g opacity="0.8" filter="url(#filter0_f_102_5932)">
          <circle cx="110.542" cy="110.542" r="60.5422" fill="#500014"/>
        </g>
        <g class="rect-ring origin-center">
          <rect x="111.018" y="79.5107" width="43.1001" height="43.1001" transform="rotate(45 111.018 79.5107)" fill="#38000E"/>
          <rect x="111.018" y="80.925" width="41.1001" height="41.1001" transform="rotate(45 111.018 80.925)" stroke="url(#paint0_linear_102_5932)" stroke-width="2"/>
          <rect x="111.018" y="83.7544" width="37.0989" height="37.0989" transform="rotate(45 111.018 83.7544)" stroke="url(#paint1_linear_102_5932)" stroke-width="0.589097"/>
        </g>
        <path opacity="0.5" fill-rule="evenodd" clip-rule="evenodd" d="M89.0258 100.337L82.0571 107.306L81.0366 106.268L88.0054 99.2988L89.0258 100.337Z" fill="#FF003F"/>
        <path opacity="0.5" fill-rule="evenodd" clip-rule="evenodd" d="M133.432 121.651L133.13 121.951L132.454 121.262L132.752 120.962L133.432 121.651ZM134.123 120.96L133.821 121.26L133.145 120.571L133.443 120.271L134.123 120.96ZM134.85 120.232L134.549 120.532L133.872 119.843L134.171 119.543L134.85 120.232ZM135.578 119.505L135.277 119.805L134.6 119.115L134.899 118.815L135.578 119.505ZM136.306 118.777L136.005 119.077L135.328 118.387L135.627 118.087L136.306 118.777ZM137.034 118.049L136.733 118.349L136.056 117.659L136.354 117.359L137.034 118.049Z" fill="#FF003F"/>
        <path class="circle-ring origin-center" opacity="0.25" d="M144.342 113.521L144.319 113.742C142.677 128.928 130.942 141.087 115.958 143.375L115.729 143.41V141.901L115.898 141.875C130.099 139.631 141.212 128.1 142.823 113.698L142.842 113.521H144.342ZM78.6851 108.425L78.6763 108.634C78.6551 109.113 78.644 109.596 78.644 110.081C78.644 110.323 78.6466 110.564 78.6519 110.805L78.6567 111.009H77.1636L77.1597 110.813C77.1545 110.57 77.1519 110.326 77.1519 110.081C77.1519 109.59 77.1623 109.102 77.1831 108.616L77.1919 108.425H78.6851ZM144.476 108.425L144.484 108.616C144.505 109.102 144.515 109.59 144.515 110.081C144.515 110.326 144.513 110.57 144.507 110.813L144.503 111.009H143.011L143.015 110.805C143.02 110.564 143.024 110.323 143.024 110.081C143.024 109.596 143.013 109.113 142.992 108.634L142.982 108.425H144.476ZM105.21 78.3799L105.046 78.4102C91.4294 80.8822 80.7967 91.9207 78.9341 105.74L78.9106 105.913H77.4067L77.436 105.688C79.3376 91.0907 90.5842 79.4311 104.976 76.9072L105.21 76.8652V78.3799ZM115.958 76.7871C130.7 79.0378 142.297 90.844 144.231 105.688L144.261 105.913H142.756L142.734 105.74C140.838 91.6769 129.86 80.4934 115.898 78.2871L115.729 78.2607V76.752L115.958 76.7871ZM105.21 143.296L104.976 143.255C90.3444 140.689 78.964 128.68 77.3491 113.742L77.3247 113.521H78.8247L78.8452 113.698C80.4282 127.855 91.1926 139.237 105.046 141.752L105.21 141.781V143.296Z" fill="#FF003F" stroke="#FF003F" stroke-width="0.4"/>

        <g class="dot dot-1" filter="url(#filter2_d_102_5932)">
          <rect x="64.9722" y="93.7158" width="3.63931" height="3.63931" transform="rotate(105.52 64.9722 93.7158)" fill="url(#paint2_radial_102_5932)"/>
        </g>

        <g class="dot dot-3" filter="url(#filter3_d_102_5932)">
          <rect x="58.2207" y="109.688" width="2.18359" height="2.18359" transform="rotate(-54.9839 58.2207 109.688)" fill="url(#paint3_radial_102_5932)"/>
        </g>

        <g class="dot dot-4" filter="url(#filter4_d_102_5932)">
          <rect x="74.7183" y="119.97" width="1.45572" height="1.45572" transform="rotate(43.6224 74.7183 119.97)" fill="url(#paint4_radial_102_5932)"/>
        </g>

        <g class="dot dot-6" filter="url(#filter5_d_102_5932)">
          <rect x="146.167" y="132.414" width="1.09179" height="1.09179" transform="rotate(34.2998 146.167 132.414)" fill="url(#paint5_radial_102_5932)"/>
        </g>

        <g class="dot dot-5" filter="url(#filter6_d_102_5932)">
          <rect x="152.346" y="109.094" width="1.81965" height="1.81965" transform="rotate(71.5539 152.346 109.094)" fill="url(#paint6_radial_102_5932)"/>
        </g>

        <g class="dot dot-2" filter="url(#filter7_d_102_5932)">
          <rect x="164.325" y="123.139" width="3.63931" height="3.63931" transform="rotate(45 164.325 123.139)" fill="url(#paint7_radial_102_5932)"/>
        </g>

        <g class="text-1" filter="url(#filter8_d_102_5932)">
          <path class="text-path" d="M115 97V125H108.841V102.619H108.671L102 106.639V101.389L109.211 97H115Z" fill="url(#paint8_linear_102_5932)"/>
        </g>

        <g class="text-2" filter="url(#filter8_d_687_2)">
          <path d="M101.766 124.133V120.026L111.362 111.141C112.178 110.351 112.862 109.64 113.415 109.009C113.977 108.377 114.403 107.758 114.692 107.153C114.982 106.538 115.127 105.876 115.127 105.165C115.127 104.375 114.947 103.695 114.587 103.125C114.227 102.546 113.736 102.103 113.113 101.795C112.49 101.479 111.783 101.322 110.993 101.322C110.168 101.322 109.449 101.488 108.835 101.822C108.22 102.155 107.747 102.633 107.413 103.256C107.08 103.879 106.913 104.621 106.913 105.481H101.503C101.503 103.717 101.902 102.186 102.701 100.887C103.499 99.5884 104.618 98.5836 106.057 97.8728C107.496 97.162 109.155 96.8066 111.033 96.8066C112.963 96.8066 114.644 97.1489 116.074 97.8333C117.513 98.509 118.632 99.448 119.431 100.65C120.229 101.852 120.629 103.23 120.629 104.783C120.629 105.801 120.427 106.806 120.023 107.798C119.628 108.789 118.922 109.891 117.904 111.102C116.886 112.304 115.451 113.747 113.6 115.432L109.664 119.289V119.473H120.984V124.133H101.766Z" fill="url(#paint8_linear_687_2)"/>
        </g>

        <g class="text-3" filter="url(#filter8_d_102_5908)">
          <path d="M110.426 124.932C108.422 124.932 106.638 124.59 105.072 123.907C103.516 123.215 102.286 122.266 101.382 121.059C100.488 119.844 100.027 118.442 100 116.854H105.85C105.886 117.519 106.105 118.105 106.508 118.61C106.919 119.107 107.465 119.493 108.145 119.768C108.825 120.043 109.59 120.181 110.44 120.181C111.325 120.181 112.108 120.025 112.788 119.715C113.468 119.404 114 118.974 114.385 118.424C114.769 117.874 114.962 117.24 114.962 116.521C114.962 115.793 114.756 115.15 114.344 114.591C113.942 114.024 113.36 113.58 112.6 113.261C111.848 112.941 110.954 112.781 109.916 112.781H107.353V108.55H109.916C110.793 108.55 111.567 108.399 112.238 108.097C112.917 107.795 113.445 107.378 113.821 106.846C114.197 106.305 114.385 105.675 114.385 104.956C114.385 104.273 114.219 103.674 113.888 103.16C113.566 102.636 113.11 102.228 112.519 101.935C111.938 101.643 111.258 101.496 110.48 101.496C109.693 101.496 108.972 101.638 108.319 101.922C107.666 102.197 107.143 102.592 106.749 103.107C106.356 103.621 106.146 104.224 106.119 104.916H100.55C100.577 103.346 101.029 101.962 101.905 100.764C102.782 99.5666 103.963 98.6306 105.448 97.9564C106.942 97.2732 108.628 96.9316 110.507 96.9316C112.403 96.9316 114.063 97.2732 115.485 97.9564C116.907 98.6395 118.012 99.5622 118.799 100.724C119.595 101.878 119.989 103.173 119.98 104.61C119.989 106.136 119.51 107.409 118.544 108.43C117.587 109.45 116.339 110.098 114.801 110.373V110.586C116.822 110.843 118.361 111.539 119.416 112.675C120.481 113.802 121.009 115.212 121 116.907C121.009 118.46 120.557 119.839 119.645 121.046C118.741 122.252 117.493 123.202 115.901 123.894C114.309 124.586 112.484 124.932 110.426 124.932Z" fill="url(#paint8_linear_102_5908)"/>
        </g>

        <text :class="{ 'translate-y-4': isHorizontal }" font-size="14" fill="#fff" x="50%" y="72%" text-anchor="middle" dominant-baseline="middle">{{ $t('countdown_to_the_next_round') }}</text>

        <defs>
          <filter id="filter0_f_102_5932" x="0" y="0" width="221.084" height="221.084" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feGaussianBlur stdDeviation="25" result="effect1_foregroundBlur_102_5932"/>
          </filter>
          <filter id="filter1_f_102_5932" x="62.876" y="61.4746" width="96.9462" height="96.9462" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feGaussianBlur stdDeviation="7.5449" result="effect1_foregroundBlur_102_5932"/>
          </filter>
          <filter id="filter2_d_102_5932" x="58.1353" y="90.3858" width="9.19324" height="9.19324" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset/>
            <feGaussianBlur stdDeviation="1.17819"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.168627 0 0 0 0 0.372549 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_102_5932"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_102_5932" result="shape"/>
          </filter>
          <filter id="filter3_d_102_5932" x="55.8643" y="105.544" width="7.75428" height="7.75379" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset/>
            <feGaussianBlur stdDeviation="1.17819"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.168627 0 0 0 0 0.372549 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_102_5932"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_102_5932" result="shape"/>
          </filter>
          <filter id="filter4_d_102_5932" x="71.3575" y="117.613" width="6.77088" height="6.77137" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset/>
            <feGaussianBlur stdDeviation="1.17819"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.168627 0 0 0 0 0.372549 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_102_5932"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_102_5932" result="shape"/>
          </filter>
          <filter id="filter5_d_102_5932" x="143.195" y="130.058" width="6.22986" height="6.23035" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset/>
            <feGaussianBlur stdDeviation="1.17819"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.168627 0 0 0 0 0.372549 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_102_5932"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_102_5932" result="shape"/>
          </filter>
          <filter id="filter6_d_102_5932" x="148.263" y="106.737" width="7.01453" height="7.01453" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset/>
            <feGaussianBlur stdDeviation="1.17819"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.168627 0 0 0 0 0.372549 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_102_5932"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_102_5932" result="shape"/>
          </filter>
          <filter id="filter7_d_102_5932" x="159.396" y="120.782" width="9.85926" height="9.85926" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset/>
            <feGaussianBlur stdDeviation="1.17819"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.168627 0 0 0 0 0.372549 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_102_5932"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_102_5932" result="shape"/>
          </filter>
          <filter id="filter8_d_102_5932" x="97.2" y="92.2" width="22.6" height="37.6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset/>
            <feGaussianBlur stdDeviation="2.4"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0 0 0 0 0 0.247059 0 0 0 0.8 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_102_5932"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_102_5932" result="shape"/>
          </filter>
          <filter id="filter8_d_687_2" x="95.5029" y="90.8066" width="31.481" height="39.3262" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset/>
            <feGaussianBlur stdDeviation="3"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0 0 0 0 0 0.247059 0 0 0 0.8 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_687_2"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_687_2" result="shape"/>
          </filter>
          <filter id="filter8_d_102_5908" x="95.2" y="92.1316" width="30.6" height="37.6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset/>
            <feGaussianBlur stdDeviation="2.4"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0 0 0 0 0 0.247059 0 0 0 0.8 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_102_5908"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_102_5908" result="shape"/>
          </filter>
          <linearGradient id="paint0_linear_102_5932" x1="111.099" y1="80.3111" x2="154.154" y2="122.475" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#FF638A"/>
            <stop offset="1" stop-color="#FF003F"/>
          </linearGradient>
          <linearGradient id="paint1_linear_102_5932" x1="111.089" y1="84.0377" x2="148.737" y2="120.907" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#FF638A"/>
            <stop offset="1" stop-color="#FF003F"/>
          </linearGradient>
          <radialGradient id="paint2_radial_102_5932" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(66.7918 95.5355) rotate(90) scale(1.81965)">
            <stop offset="0" stop-color="#FF6E92"/>
            <stop offset="1" stop-color="#FF2B5F"/>
          </radialGradient>
          <radialGradient id="paint3_radial_102_5932" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(59.3125 110.78) rotate(90) scale(1.09179)">
            <stop offset="0" stop-color="#FF6E92"/>
            <stop offset="1" stop-color="#FF2B5F"/>
          </radialGradient>
          <radialGradient id="paint4_radial_102_5932" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(75.4461 120.698) rotate(90) scale(0.727862)">
            <stop offset="0" stop-color="#FF6E92"/>
            <stop offset="1" stop-color="#FF2B5F"/>
          </radialGradient>
          <radialGradient id="paint5_radial_102_5932" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(146.712 132.96) rotate(90) scale(0.545896)">
            <stop offset="0" stop-color="#FF6E92"/>
            <stop offset="1" stop-color="#FF2B5F"/>
          </radialGradient>
          <radialGradient id="paint6_radial_102_5932" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(153.256 110.004) rotate(90) scale(0.909827)">
            <stop offset="0" stop-color="#FF6E92"/>
            <stop offset="1" stop-color="#FF2B5F"/>
          </radialGradient>
          <radialGradient id="paint7_radial_102_5932" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(166.145 124.958) rotate(90) scale(1.81965)">
            <stop offset="0" stop-color="#FF6E92"/>
            <stop offset="1" stop-color="#FF2B5F"/>
          </radialGradient>
          <linearGradient id="paint8_linear_102_5932" x1="106.694" y1="96.9272" x2="106.694" y2="124.857" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#FFFFFF"/>
            <stop offset="1" stop-color="#FF678D"/>
          </linearGradient>
          <linearGradient id="paint8_linear_102_5908" x1="110.809" y1="97.2336" x2="110.809" y2="124.42" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="white"/>
            <stop offset="1" stop-color="#FF678D"/>
          </linearGradient>
          <linearGradient id="paint8_linear_687_2" x1="111.191" y1="96.9506" x2="111.191" y2="124.133" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="white"/>
            <stop offset="1" stop-color="#FF678D"/>
          </linearGradient>
        </defs>
      </svg>
    </div>
  </div>
</template>

<style scoped>
:where([data-layout="x"]) .countdown-box {
  width: 180px;
  height: 180px;
}
:where([data-layout="y"]) .countdown-box {
  width: 220px;
  height: 220px;
}
</style>

