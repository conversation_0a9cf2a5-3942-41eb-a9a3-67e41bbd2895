import {createApp, h} from "vue";
import type { MessageOptions } from "./types";

import GMessage from '@/components/ui/GMessage/index.vue';
import emitter from "@/utils/emitter.ts";

export function openMessage (props: MessageOptions = {}) {
  emitter.emit('play-sound', 'SOUND_4');

  const msgContainer = document.createElement('div');
  const root = document.querySelector('#view-box');

  if (!root) return console.error('not fount container');

  root.appendChild(msgContainer);

  const msg = createApp({
    render() {
      return h(GMessage, {
        ...props,
        onClose: close
      });
    },
  });

  const close = () => {
    msg.unmount();
    msgContainer.remove();
  }

  msg.mount(msgContainer);

  return { close }
}