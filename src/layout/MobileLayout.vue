<script setup lang="ts">
import MobileHeaderNav from "@/components/mobile/MobileHeaderNav.vue";
import MobileVideoBox from "@/components/mobile/MobileVideoBox.vue";
import MobileTabs from "@/components/mobile/MobileTabs.vue";
import Settlement from "@/components/widget/settlement/Index.vue";
import CameraTransition from "@/components/CameraTransition/index.vue";
import CountDown from "@/components/CountDown/index.vue";
import MobileVideoArrow from "@/components/mobile/MobileVideoArrow.vue";
</script>

<template>
  <div class="view-lobby">
    <div class="header-wrapper">
      <MobileHeaderNav/>
    </div>
    <div class="video-wrapper relative correct-container">
      <MobileVideoBox />
      <CountDown />
      <Settlement />
      <CameraTransition />
      <MobileVideoArrow />
    </div>
    <div class="panel-wrapper">
      <MobileTabs/>
    </div>
  </div>
</template>

<style scoped>

</style>