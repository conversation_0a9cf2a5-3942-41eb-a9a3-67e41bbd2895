<script setup lang="ts">
import {computed, nextTick, onMounted, watch} from "vue";
import {storeToRefs} from "pinia";
import {createScope, createTimeline, stagger, utils} from "animejs";
import {sleep} from "radash";
import {useGameStore, useMainStore} from "@/stores";
import { GameStatus } from "@/constant/gameData.ts";

import MedalItem from "./MedalItem.vue";
import BallItem from "./BallItem.vue";

const { isHorizontal, isVertical } = storeToRefs(useMainStore());
const { gameRoundId, gameResult, gameStatus } = storeToRefs(useGameStore());

createScope({ root: '.settlement-box' });

const showResult = computed(() => {
  return [GameStatus.GAME_END, GameStatus.GAME_SETTLEMENT].includes(gameStatus.value as GameStatus)
});

const setupDefault = () => {
  if (isHorizontal.value) {
    utils.set('.settlement-box', { height: 0, opacity: 0 });
    utils.set('.x-arrow-left', { x: "-100%", rotateZ: 180 });
    utils.set('.x-arrow-right', { x: "100%", rotateZ: 0 });
  } else {
    utils.set('.settlement-box', { opacity: 0 });
    utils.set('.y-arrow-left', { x: "-100%", rotateZ: 0 });
    utils.set('.y-arrow-right', { x: "100%", rotateZ: 180 });
  }
  utils.set('.result-title', { y: "-100%", opacity: 0 });
  utils.set('.ball-item', { y: -30, opacity: 0 });
}

const setupShowAnim = () => {
  const tl = createTimeline({ defaults: { duration: 300, ease: 'out(1.675)' } });
  if (isHorizontal.value) {
    tl.add('.settlement-box', { height: 136, opacity: 1 });
    tl.add('.x-arrow-left', { x: 0, rotateZ: 180, opacity: 1 });
    tl.add('.x-arrow-right', { x: 0, rotateZ: 0, opacity: 1 }, '<<');
  } else {
    tl.add('.settlement-box', { opacity: 1 });
    tl.add('.y-arrow-left', { x: 0, rotateZ: 0, opacity: 1 });
    tl.add('.y-arrow-right', { x: 0, rotateZ: 180, opacity: 1 }, '<<');
  }
  tl.add('.result-title', { y: 0, opacity: 1 }, stagger(200));
  tl.add('.ball-item.ball-no-1', { y: 0, opacity: 1 }, stagger(200));
  tl.add('.ball-item.ball-no-2', { y: 0, opacity: 1 }, '-=200');
  tl.add('.ball-item.ball-no-3', { y: 0, opacity: 1 }, '-=200');
  tl.add('.ball-item.ball-other', { y: 0, opacity: 1 }, stagger(200));
}

const setupShow = async () => {
  setupDefault();
  await sleep(300)
  setupShowAnim();
}

watch(showResult, (show: boolean) => {
  show && nextTick(setupShow);
});

onMounted(() => {
  showResult.value && setupShow();
});
</script>

<template>
  <div>
    <div v-if="showResult" :class="{ 'h-34': isHorizontal, 'h-full': isVertical }" class="settlement-box flex flex-col items-center">
      <div :class="{ 'bg-black/70': isHorizontal, 'bg-black': isVertical }" class="absolute inset-0 size-full overflow-hidden">
        <div :class="{ 'hidden': isVertical }">
          <!--左箭头-->
          <div class="x-arrow-left flex items-center absolute rotate-z-180 left-4 top-0 bottom-0 m-auto">
            <svg xmlns="http://www.w3.org/2000/svg" width="124" height="137" viewBox="0 0 124 137" fill="none">
            <g>
              <path fill-rule="evenodd" clip-rule="evenodd" d="M96.9804 -15.7014V-18H72.3883L0 69.1992L71.3798 155.184H96.9804V154.1L26.5004 69.1992L96.9804 -15.7014Z" fill="url(#paint0_linear_323_299)" fill-opacity="0.3"/>
              <path d="M96.4805 -17.5V-15.8818L26.1152 68.8799L25.8506 69.1992L26.1152 69.5186L96.4805 154.279V154.684H71.6143L0.649414 69.1992L72.623 -17.5H96.4805Z" stroke="url(#paint1_linear_323_299)" stroke-opacity="0.5"/>
              <defs>
                <linearGradient id="paint0_linear_323_299" x1="48.4902" y1="0.499999" x2="48.4902" y2="135.5" gradientUnits="userSpaceOnUse">
                  <stop offset="0" stop-color="#FC013F" stop-opacity="0"/>
                  <stop offset="0.481233" stop-color="#FC013F"/>
                  <stop offset="1" stop-color="#FC013F" stop-opacity="0"/>
                </linearGradient>
                <linearGradient id="paint1_linear_323_299" x1="0.000976563" y1="69" x2="17.501" y2="69" gradientUnits="userSpaceOnUse">
                  <stop offset="0" stop-color="#FB013E"/>
                  <stop offset="1" stop-color="#FB013E" stop-opacity="0"/>
                </linearGradient>
              </defs>
            </g>

            <g opacity="0.7" transform="translate(70, 4)">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M53.5701 31.9871V0L0 64.6552L53.5701 129.31V97.3232L26.503 64.6552L53.5701 31.9871Z" fill="url(#paint0_linear_323_301)" fill-opacity="0.2"/>
              <path d="M53.0703 31.8066L26.1182 64.3359L25.8535 64.6553L26.1182 64.9746L53.0703 97.5029V127.923L0.648438 64.6553L53.0703 1.38672V31.8066Z" stroke="url(#paint1_linear_323_301)" stroke-opacity="0.5"/>
              <defs>
                <linearGradient id="paint0_linear_323_301" x1="26.7851" y1="13.8133" x2="26.7851" y2="114.613" gradientUnits="userSpaceOnUse">
                  <stop offset="0" stop-color="#FC013F" stop-opacity="0"/>
                  <stop offset="0.481233" stop-color="#FC013F"/>
                  <stop offset="1" stop-color="#FC013F" stop-opacity="0"/>
                </linearGradient>
                <linearGradient id="paint1_linear_323_301" x1="0.000539435" y1="64.96" x2="9.66721" y2="64.96" gradientUnits="userSpaceOnUse">
                  <stop offset="0" stop-color="#FB013E"/>
                  <stop offset="1" stop-color="#FB013E" stop-opacity="0"/>
                </linearGradient>
              </defs>
            </g>
          </svg>
          </div>
          <!--右箭头-->
          <div class="x-arrow-right flex items-center absolute rotate-z-0 right-4 top-0 bottom-0 m-auto">
            <svg xmlns="http://www.w3.org/2000/svg" width="124" height="137" viewBox="0 0 124 137" fill="none">
            <g>
              <path fill-rule="evenodd" clip-rule="evenodd" d="M96.9804 -15.7014V-18H72.3883L0 69.1992L71.3798 155.184H96.9804V154.1L26.5004 69.1992L96.9804 -15.7014Z" fill="url(#paint0_linear_323_299)" fill-opacity="0.3"/>
              <path d="M96.4805 -17.5V-15.8818L26.1152 68.8799L25.8506 69.1992L26.1152 69.5186L96.4805 154.279V154.684H71.6143L0.649414 69.1992L72.623 -17.5H96.4805Z" stroke="url(#paint1_linear_323_299)" stroke-opacity="0.5"/>
              <defs>
                <linearGradient id="paint0_linear_323_299" x1="48.4902" y1="0.499999" x2="48.4902" y2="135.5" gradientUnits="userSpaceOnUse">
                  <stop offset="0" stop-color="#FC013F" stop-opacity="0"/>
                  <stop offset="0.481233" stop-color="#FC013F"/>
                  <stop offset="1" stop-color="#FC013F" stop-opacity="0"/>
                </linearGradient>
                <linearGradient id="paint1_linear_323_299" x1="0.000976563" y1="69" x2="17.501" y2="69" gradientUnits="userSpaceOnUse">
                  <stop offset="0" stop-color="#FB013E"/>
                  <stop offset="1" stop-color="#FB013E" stop-opacity="0"/>
                </linearGradient>
              </defs>
            </g>

            <g opacity="0.7" transform="translate(70, 4)">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M53.5701 31.9871V0L0 64.6552L53.5701 129.31V97.3232L26.503 64.6552L53.5701 31.9871Z" fill="url(#paint0_linear_323_301)" fill-opacity="0.2"/>
              <path d="M53.0703 31.8066L26.1182 64.3359L25.8535 64.6553L26.1182 64.9746L53.0703 97.5029V127.923L0.648438 64.6553L53.0703 1.38672V31.8066Z" stroke="url(#paint1_linear_323_301)" stroke-opacity="0.5"/>
              <defs>
                <linearGradient id="paint0_linear_323_301" x1="26.7851" y1="13.8133" x2="26.7851" y2="114.613" gradientUnits="userSpaceOnUse">
                  <stop offset="0" stop-color="#FC013F" stop-opacity="0"/>
                  <stop offset="0.481233" stop-color="#FC013F"/>
                  <stop offset="1" stop-color="#FC013F" stop-opacity="0"/>
                </linearGradient>
                <linearGradient id="paint1_linear_323_301" x1="0.000539435" y1="64.96" x2="9.66721" y2="64.96" gradientUnits="userSpaceOnUse">
                  <stop offset="0" stop-color="#FB013E"/>
                  <stop offset="1" stop-color="#FB013E" stop-opacity="0"/>
                </linearGradient>
              </defs>
            </g>
          </svg>
          </div>
        </div>
        <div :class="{ 'hidden': isHorizontal }">
          <!--左箭头-->
          <div class="y-arrow-left flex items-center absolute rotate-z-0 left-0 top-0 bottom-0 m-auto">
            <svg width="37" height="174" viewBox="0 0 37 174" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M-60.0001 2.7605V0.461914H-35.525L36.5186 87.6611L-34.5213 173.645H-60.0001V172.562L10.1444 87.6611L-60.0001 2.7605Z" fill="#FC013F" fill-opacity="0.2"/>
            <path d="M-59.5 0.961914V2.58008L10.5303 87.3428L10.793 87.6611L10.5303 87.9795L-59.5 172.741V173.146H-34.7568L35.8701 87.6602L-35.7598 0.961914H-59.5Z" stroke="url(#paint0_linear_100_2068)" stroke-opacity="0.5"/>
            <defs>
              <linearGradient id="paint0_linear_100_2068" x1="36.5176" y1="87.4619" x2="19.1009" y2="87.4619" gradientUnits="userSpaceOnUse">
                <stop offset="0" stop-color="#FB013E"/>
                <stop offset="1" stop-color="#FB013E" stop-opacity="0"/>
              </linearGradient>
            </defs>
          </svg>
          </div>
          <!--右箭头-->
          <div class="y-arrow-right flex items-center absolute rotate-z-180 right-0 top-0 bottom-0 m-auto">
            <svg width="37" height="174" viewBox="0 0 37 174" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M-60.0001 2.7605V0.461914H-35.525L36.5186 87.6611L-34.5213 173.645H-60.0001V172.562L10.1444 87.6611L-60.0001 2.7605Z" fill="#FC013F" fill-opacity="0.2"/>
            <path d="M-59.5 0.961914V2.58008L10.5303 87.3428L10.793 87.6611L10.5303 87.9795L-59.5 172.741V173.146H-34.7568L35.8701 87.6602L-35.7598 0.961914H-59.5Z" stroke="url(#paint0_linear_100_2068)" stroke-opacity="0.5"/>
            <defs>
              <linearGradient id="paint0_linear_100_2068" x1="36.5176" y1="87.4619" x2="19.1009" y2="87.4619" gradientUnits="userSpaceOnUse">
                <stop offset="0" stop-color="#FB013E"/>
                <stop offset="1" stop-color="#FB013E" stop-opacity="0"/>
              </linearGradient>
            </defs>
          </svg>
          </div>
        </div>
      </div>
      <div :class="{ 'pt-2.5': isHorizontal, 'pt-5': isVertical }" class="relative">
        <!--结算标题栏-->
        <div class="result-title flex justify-center items-center">
          <img src="@/assets/svg/arrow-gold-left.svg" alt="arrow left" class="w-auto h-[12px]">
          <span class="settlement-title mx-1 text-default">NO.{{ gameRoundId }} LOTTERY RESULTS</span>
          <img src="@/assets/svg/arrow-gold-right.svg" alt="arrow right" class="w-auto h-[12px]">
        </div>

        <div :class="{ 'mt-4 gap-5': isHorizontal, 'mt-9 gap-2': isVertical }" class="flex justify-center">
          <!--银、金、铜牌-->
          <MedalItem
            v-for="n in [1,0,2]"
            :key="n"
            :ball="gameResult[n]"
            :order="n + 1"
            :class="`ball-item ball-no-${n + 1}`"
          />
        </div>

        <div :class="{ 'gap-9': isHorizontal, 'gap-4': isVertical }" class="mt-[5px] flex justify-center">
          <!--排名4-10号的球-->
          <BallItem
            v-for="(ball, index) of gameResult.slice(3)"
            :key="index"
            :ball="ball"
            :order="index + 4"
            :class="`ball-item ball-other ball-no-${index + 4}`"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.settlement-box {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
}

.settlement-title {
  background: linear-gradient(to top, white 0%, #E0C77C 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}
</style>
