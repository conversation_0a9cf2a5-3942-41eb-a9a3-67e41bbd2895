<script setup lang="ts">
import {useRouter} from "vue-router";
import ToolBar from "@/components/widget/ToolBar.vue";
import TimeInfo from "@/components/widget/TimeInfo.vue";

const router = useRouter();

const handleBack = () => {
  router.back();
}
</script>

<template>
  <header class="mobile-header-nav">
    <div class="flex items-center justify-start">
      <img class="block w-5" src="/images/back.png" alt="back" @click="handleBack" />
    </div>

    <TimeInfo />

    <div class="flex items-center justify-end">
      <ToolBar :toolbar="['rule']" />
    </div>
  </header>
</template>

<style scoped>
.mobile-header-nav {
  display: grid;
  grid-template-columns: minmax(0, 1fr) auto minmax(0, 1fr);
  grid-template-rows: 30px;
  align-items: center;
  width: 100%;
  padding-inline: 12px;
  padding-top: 10px;
  padding-bottom: 5px;
}
</style>