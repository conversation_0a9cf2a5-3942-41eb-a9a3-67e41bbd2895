import { storeToRefs } from "pinia";
import { animate } from "animejs";
import { useGameStore, usePersistStore, useUserStore } from "@/stores";
import { useSocketFetch } from "@/composables/useSocketFetch";
import { getBoundingClientRect } from "@/utils/utils";
import emitter from "@/utils/emitter";

const getElementCenter = (element: Element) => {
  const {width = 0, left = 0, height = 0, top = 0} = getBoundingClientRect(element) ?? {};
  return {
    width,
    height,
    x: (left + width / 2),
    y: (top + height / 2),
  };
};

const getRootContainer = () => document.querySelector('#view-box');

/**
 * 播放投注动画
 * @param chip 当前选中的筹码
 * @param target 目标区域
 * @param size 结束时的大小
 * @param cb 播放完成后的回调
 */
export const playBetAnim = (chip: Element, target: HTMLElement, size: number, cb?: () => void) => {
  const rootContainer = getRootContainer();
  const el = document.createElement("div");
  const { left: startX, top: startY } = getBoundingClientRect(chip) ?? {};
  const { width: chipW, y: chipTop, x: chipLeft } = getElementCenter(chip) ?? {};
  const { y: targetTop, x: targetLeft } = getElementCenter(target) ?? {};
  const endTx = targetLeft - chipLeft;
  const endTy = targetTop - chipTop;
  const scale = size / chipW;

  el.style.cssText = `
    position: absolute;
    z-index: 999;
    top: ${startY}px;
    left: ${startX}px;
    width: ${chipW}px;
    height: ${chipW}px;
    pointer-events: none;
    background-image: url(/images/bets-1.png);
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
  `;

  rootContainer?.appendChild(el);

  animate(el, {
    translateX: `${endTx}px`,
    translateY: `${endTy}px`,
    scale: `${scale}`,
    duration: 500,
    onComplete: (self) => {
      self.pause();
      el.remove();
      cb?.();
    }
  });
}

/**
 * 播放撤销投注动画
 * @param chips 被撤销的筹码列表
 * @param target 目标区域
 * @param cb 播放完成后的回调
 */
export const playBackBetAnim = (chips: HTMLImageElement[], target: HTMLElement, cb?: () => void) => {
  const rootContainer = getRootContainer();
  const { width: chipW, y: chipTop, x: chipLeft } = getElementCenter(target) ?? {};

  chips.forEach((item, index) => {
    const itemCenter = getElementCenter(item) ?? {};
    const { left: itemLeft = 0, top: itemTop = 0, width: itemWidth = 0 } = getBoundingClientRect(item) ?? {};
    const deltaX = chipLeft - itemCenter.x;
    const deltaY = chipTop - itemCenter.y;
    const flyingChips = item.cloneNode(true) as HTMLImageElement;

    flyingChips.style.cssText = `
      position: fixed;
      z-index: 999;
      left: ${itemLeft}px;
      top: ${itemTop}px;
    `;

    rootContainer?.appendChild(flyingChips);

    animate(flyingChips, {
      translateX: `${deltaX}px`,
      translateY: `${deltaY}px`,
      scale: chipW / itemWidth,
      duration: 500,
      onBegin: () => {
        // 移除通过template下的v-if来控制
        item.style.opacity = '0';
      },
      onComplete: (self) => {
        self.pause();
        flyingChips.remove();
        if (index === chips.length - 1) cb?.();
      },
    });
  });
}

/**
 * 播放筹码到余额
 * @param chips 中奖的筹码列表
 * @param target 目标元素（余额元素）
 * @param cb 播放完成后的回调
 */
export const playToBalanceAnim = (chips: HTMLImageElement[], target: HTMLElement, cb?: () => void) => {
  const rootContainer = getRootContainer();
  const { x: targetLeft, y: targetTop } = getElementCenter(target) ?? {};

  chips.forEach((img, index) => {
    const itemCenter = getElementCenter(img) ?? {};
    const { left: itemLeft = 0, top: itemTop = 0 } = getBoundingClientRect(img) ?? {};
    const deltaX = targetLeft - itemCenter.x;
    const deltaY = targetTop - itemCenter.y;
    const flyingChips = img.cloneNode(true) as HTMLImageElement;

    flyingChips.style.cssText = `
      position: fixed;
      z-index: 999;
      left: ${itemLeft}px;
      top: ${itemTop}px;
    `;

    rootContainer?.appendChild(flyingChips);

    animate(flyingChips, {
      keyframes: {
        '0%': { scale: 1, translateX: 0, translateY: 0 },
        '15%': { scale: 1.2, translateX: 0, translateY: 0 },
        '30%': { scale: 1, translateX: 0, translateY: 0 },
        '45%': { scale: 1.2, translateX: 0, translateY: 0 },
        '60%': { scale: 1, translateX: 0, translateY: 0 },
        '65%': { scale: 1, translateX: 0, translateY: 0 },
        '100%': { translateX: deltaX, translateY: deltaY, scale: 1 }
      },
      duration: 1800,
      onBegin: () => {
        // 移除通过template下的v-if来控制
        img.style.opacity = '0';
      },
      onComplete: (self) => {
        self.pause();
        flyingChips.remove();
        if (index === chips.length - 1) cb?.();
      },
    });
  });
}

/**
 * 投注请求
 * @param data 
 * @returns 
 */
export const handleBet = (data: any) => {
  const { promise } = useSocketFetch(
    'user-bet-req',
    'user-bet-res',
    data
  );

  return promise;
}

/**
 * 撤销投注请求
 * @param data 
 * @returns 
 */
export const handleRevokeBet = (data: any) => {
  const { promise } = useSocketFetch(
    'user-revoke-req',
    'user-revoke-res',
    data,
  );

  return promise;
}

/**
 * 本地计算余额更新
 * @param deductAmount 扣除金额（投注金额）
 */
export const handleUpdateLocalBalance = (deductAmount: number) => {
  const { setBalance } = useUserStore();
  const { balance } = storeToRefs(useUserStore());
  setBalance((balance.value * 1e3 - deductAmount * 1e3) / 1e3);
}

/**
 * 本地更新投注信息
 * @param betInfo 投注信息
 */
export const handleUpdateLocalBetInfo = (betInfo: Pick<BetItemRes, 'betAmount' | 'betLocation' | 'playTypeId'>) => {
  const { updatePlayerBetInfo } = usePersistStore();
  updatePlayerBetInfo(betInfo);
}
