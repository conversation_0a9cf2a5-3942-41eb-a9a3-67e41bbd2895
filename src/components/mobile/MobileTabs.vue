<script setup lang="ts">
import {onMounted, ref, useTemplateRef} from "vue";
import {storeToRefs} from "pinia";
import {useMainStore} from "@/stores";
import {getBoundingClientRect, rotate90Swiper} from "@/utils/utils.ts";
import emitter from "@/utils/emitter.ts";

import MobileBetPanel from "@/components/mobile/MobileBetPanel.vue";
import MobileHistoryPanel from "@/components/mobile/MobileHistoryPanel.vue";
import MobileRecordPanel from "@/components/mobile/MobileRecordPanel.vue";

const { isRotation } = storeToRefs(useMainStore());

const tabBoxRef = useTemplateRef('tabBoxRef');
const swiperRef = useTemplateRef<any>('swiperRef');
const tabItemMapRef = ref<{ [key: number]: any }>({});

const actIdx = ref(0);

const onTabClick = (idx: number) => {
  emitter.emit('play-sound', 'SOUND_1');
  swiperRef.value?.swiper?.slideTo(idx);
};

const updateTabs = (idx: number) => {
  actIdx.value = idx;
  const actTab = tabItemMapRef.value?.[idx];
  if (actTab && tabBoxRef.value) {
    const { left, width } = getBoundingClientRect(actTab)
    const offset = left + width / 2;
    tabBoxRef.value.style.setProperty('--decoration-underline-x', `${offset}px`);
  }
}

onMounted(() => {
  updateTabs(0);
  if (swiperRef.value) {
    // 初始化时应用旋转逻辑
    isRotation.value && rotate90Swiper(swiperRef.value.swiper, true);
    swiperRef.value.addEventListener('swiperslidechange', (event: CustomEvent) => {
      const targetSwiper = event.detail?.[0];
      // 修复筹码swiper切换触发当前swiperslidechange问题
      if (targetSwiper && targetSwiper === swiperRef.value.swiper) {
        const active = targetSwiper.activeIndex;
        updateTabs(active);
      }
    });
  }
});
</script>

<template>
  <div>
    <div ref="tabBoxRef" class="tabs-wrapper">
      <div class="flex justify-start w-20">
        <div :ref="el => tabItemMapRef[0] = el" class="tabs-item" @click="onTabClick(0)">
          {{ $t('bets') }}
        </div>
      </div>

      <div class="mx-auto">
        <div :ref="el => tabItemMapRef[1] = el" class="tabs-item" @click="onTabClick(1)">
          {{ $t('trend_history') }}
        </div>
      </div>

      <div class="flex justify-end w-20">
        <div :ref="el => tabItemMapRef[2] = el" class="tabs-item" @click="onTabClick(2)">
          {{ $t('records') }}
        </div>
      </div>
    </div>

    <div ref="panelBoxRef" class="panels-wrapper scrollbar-hidden">
      <swiper-container ref="swiperRef" class="h-full">
        <swiper-slide>
          <section class="panels-item" data-index="0">
            <MobileBetPanel :active="actIdx === 0" />
          </section>
        </swiper-slide>
        <swiper-slide>
          <section class="panels-item" data-index="1">
            <MobileHistoryPanel :active="actIdx === 1" />
          </section>
        </swiper-slide>
        <swiper-slide>
          <section class="panels-item" data-index="2">
            <MobileRecordPanel :active="actIdx === 2" />
          </section>
        </swiper-slide>
      </swiper-container>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.tabs-wrapper {
  --decoration-underline-x: 0px;

  width: 100%;
  overflow-x: auto;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  position: relative;
  display: flex;
  justify-content: space-between;

  &::after {
    position: absolute;
    bottom: 0;
    left: 0;
    content: '';
    width: 16px;
    height: 3px;
    background-color: #FF003F;
    transform: translateX(calc(var(--decoration-underline-x) - 50%));
    transition: transform 0.3s ease;
  }

  .tabs-item {
    flex-shrink: 0;
    padding-inline: 12px;
    height: 36px;
    line-height: 36px;
    font-size: 14px;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.9);
    transition: color 0.3s ease;

    &.active {
      font-weight: 700;
      color: rgba(255, 255, 255, 1);
    }
  }
}
</style>