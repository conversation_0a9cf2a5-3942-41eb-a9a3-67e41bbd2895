<template>
  <div class="mt-2 mb-2">
    <div class="flex justify-center items-center flex-col">
      <div class="history-tab">
        <div :class="{ 'tab-btn': true, active: isActive }" @click="changeTab(true)">
          {{ $t('top') }}
        </div>
        <div :class="{ 'tab-btn': true, active: !isActive }" @click="changeTab(false)">
          {{ $t('odd_and_even') }}
        </div>
      </div>
    </div>
    <GLoading :show="loading" class="history-lists scrollbar-hidden">
      <GEmpty v-if="!historyTrend.length" class="mt-10" />
      <template v-else>
        <section v-show="isActive">
          <div v-for="item in historyTrend" :key="item.roundId" class="list-item">
            <p class="item-title">NO.{{ item.roundId }}</p>
            <div class="item-imgs">
              <img
                v-for="num in item.result?.split(',')"
                :key="num"
                :src="`/images/ball-${num}.png`"
                alt="ball"
              />
            </div>
          </div>
        </section>

        <section v-show="!isActive">
          <table>
            <tr v-for="item in oddAndEvenList" :key="item.id">
              <td v-for="index in 9" :key="index">
                <template v-if="!item.even">
                  <img v-show="item.count >= index" :src="`/images/odd.png`" alt=""/>
                </template>
                <template v-if="item.even">
                  <img v-show="item.count >= index" :src="`/images/even.png`" alt=""/>
                </template>
              </td>
              <td >{{ item.count > 9 ? `+${item.count - 9}` : '' }}</td>
            </tr>
          </table>
        </section>
      </template>
    </GLoading>
  </div>
</template>

<script setup lang="ts">
import {ref, computed, watchEffect} from "vue";
import {storeToRefs} from "pinia";
import {last} from "radash";
import {useSocketFetch} from "@/composables/useSocketFetch";
import {useGameStore} from "@/stores";

import GLoading from "@/components/ui/GLoading/index.vue"
import GEmpty from "@/components/ui/GEmpty/index.vue"

const { historyTrend } = storeToRefs(useGameStore());
const { setHistoryBallRate, setHistoryTrend } = useGameStore();

const props = defineProps<{
  active: boolean;
}>();

// 用于控制tab的激活状态
const isActive = ref(true);

const loading = ref(false);

const fetchData = async () => {
  loading.value = true;
  try {
    const { promise } = useSocketFetch('get-game-history-req', 'get-game-history-res');
    const res = await promise;
    const { data, code } = res?.data || {};
    if (code === 200) {
      setHistoryBallRate(data?.rateMap ?? {});
      setHistoryTrend(data?.historyResultVOList ?? []);
    }
  } catch (e) {
    console.error('fetch data error', e);
  } finally {
    loading.value = false;
  }
}

const changeTab = (b: boolean) => {
  if (isActive.value === b) return;
  isActive.value = b;
  fetchData();
};

const oddAndEvenList = computed(() => {
  const result: Array<{
    id: number;
    even: boolean;
    count: number;
  }> = [];

  let row = 0;

  (historyTrend.value || []).forEach(item => {
    if (result.length === 0) {
      result.push({ id: row, even: item.even, count: 1 });
      return;
    }

    const lastItem = last(result, {} as any);

    if (lastItem?.even === item.even) {
      lastItem.count++;
    } else {
      result.push({ id: ++row, even: item.even, count: 1 });
    }
  });

  return result;
});

watchEffect(() => {
  props.active && fetchData();
});
</script>

<style scoped>
.history-tab {
  min-width: 240px;
  height: 40px;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.1);
  margin: 0 auto;
  padding: 4px 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tab-btn {
  min-width: 112px;
  padding-inline: 4px;
  height: 32px;
  font-size: 14px;
  text-align: center;
  line-height: 32px;
  color: #ffffff;
}

.tab-btn.active {
  background: linear-gradient(180deg, #72001c 0%, #910024 70.3%, #d50035 100%);
  border-radius: 4px;
  font-weight: 700;
}

.history-lists {
  max-height: 420px;
  padding: 0 12px;
  overflow: auto;
}

.list-item {
  border-bottom: 1px solid #292a2c;
  margin: 16px 0;
}

.item-title {
  font-size: 12px;
  font-weight: 400;
  color: #ffffff;
  margin-bottom: 12px;
}

.item-imgs {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.item-imgs img {
  width: 28px;
  height: 28px;
}

table {
  border-collapse: collapse;
  width: 100%;
  border: 1px solid #363538; /* 添加表格边框 */
  margin-top: 12px;
}

tr {
  border: 1px solid #363538; /* 添加行边框 */
  height: 30px;
}

td {
  width: 30px;
  text-align: center;
  border: 1px solid #363538; /* 添加单元格边框 */
  line-height: 30px;
  color: #ffffff;
  font-size:12px ;
  font-weight:700 ;
}

td:nth-last-child(1){
  width: 50px;
}

td img {
  width: 26px;
  height: 26px;
  margin: 0 auto;
}
</style>
