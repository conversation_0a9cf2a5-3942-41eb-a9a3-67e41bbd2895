<script setup lang="ts">
import {onMounted, ref} from "vue";
import {useI18n} from "vue-i18n";
import {storeToRefs} from "pinia";
import {useGameStore, useMainStore} from "@/stores";

import GModal from '@/components/ui/GModal/index.vue';
import GSelect from "@/components/ui/GSelect/index.vue";
import GSlider from '@/components/ui/GSlider/index.vue';

const showModal = ref<boolean>(false);

const { locale } = useI18n();
const { videoQualityList } = storeToRefs(useGameStore());
const { langs, lang, mainVolume, soundVolume, bmgVolume, cameraInfo } = storeToRefs(useMainStore());
const { setLang } = useMainStore();

const handleLang = (lang: string | number) => {
  locale.value = lang as string;
  setLang(lang as string);
}

const show = () => {
  showModal.value = true;
}

const hide = () => {
  showModal.value = false;
}

onMounted(() => {
  show();
});

defineExpose({ show, hide })
</script>

<template>
  <GModal v-model:show="showModal" :title="$t('set_up')">
    <div class="px-1 select-none">
      <div class="pb-4 text-white">
        <p class="text-white text-sm">{{ $t('master_volume') }}</p>
        <GSlider v-model="mainVolume" />
      </div>
      <div class="pb-4 text-white">
        <p class="text-white text-sm">{{ $t('sound_effects') }}</p>
        <GSlider v-model="soundVolume" />
      </div>
      <div class="pb-4 text-white">
        <p class="text-white text-sm">{{ $t('background_music') }}</p>
        <GSlider v-model="bmgVolume" />
      </div>
      <div class="py-4 text-white flex items-center justify-between border-b border-[#302f32]">
        <p class="text-white text-sm">{{ $t('definition') }}</p>
        <GSelect v-model="cameraInfo.quality" :options="videoQualityList.map(item => ({ label: `${item}P`, value: item }))" />
      </div>
      <div class="py-4 text-white flex items-center justify-between">
      <p class="text-white text-sm">{{ $t('language') }}</p>
      <GSelect :value="lang" :options="langs" @change="handleLang" />
    </div>
    </div>
  </GModal>
</template>

<style scoped>

</style>