<template>
  <GModal v-model:show="showModal" :width="400" :title="$t('records')">
    <Records />
  </GModal>
</template>

<script setup lang="ts">
import {onMounted, ref} from "vue";
import GModal from "@/components/ui/GModal/index.vue";
import Records from "@/components/widget/Records.vue";

const showModal = ref<boolean>(false);

const show = () => {
  showModal.value = true;
}

const hide = () => {
  showModal.value = false;
}

onMounted(() => {
  show();
});

defineExpose({ show, hide })
</script>

<style>

</style>
