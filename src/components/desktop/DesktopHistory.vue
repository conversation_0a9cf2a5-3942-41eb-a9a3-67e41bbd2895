<template>
  <div v-if="isShowXHistory" ref="historyPanelRef" class="x-history-panel" :class="{ 'has-full': !isShowXBetPanel }">
    <div class="flex flex-col size-full bg-black/50">
      <div class="grid grid-cols-[minmax(0,1fr)_auto_minmax(0,1fr)] mb-1 text-white leading-5 gap-3">
        <section class="bg-white/30 flex align-center w-max h-5">
          <div class="tabs-item" :class="{ active: isActive }" @click="handleTabChange(true)">
            {{ $t('top') }}
          </div>
          <div class="tabs-item" :class="{ active: !isActive }" @click="handleTabChange(false)">
            {{ $t('odd_and_even') }}
          </div>
        </section>
        <section class="text-center">
          {{ $t('trend_history') }}
        </section>
        <section class="flex items-center justify-end pr-1.5">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="12"
            height="12"
            viewBox="0 0 12 12"
            fill="none"
            class="cursor-pointer transition-all hover:rotate-90"
            @click="toggleXHistoryShow()"
          >
            <rect width="2.29124" height="14.5749" transform="matrix(0.692704 -0.721222 0.704535 0.709669 0 1.65234)"
                  fill="white" />
            <rect width="2.29124" height="14.5749"
                  transform="matrix(-0.692704 -0.721222 -0.704535 0.709669 12 1.65234)" fill="white" />
          </svg>
        </section>
      </div>
      <div class="flex-1 overflow-hidden">
        <GLoading :show="loading" class="size-full">
          <HistoryTop v-show="isActive" :is-expand="isExpand" />
          <HistoryOdd v-show="!isActive" :is-expand="isExpand" />
        </GLoading>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {onMounted, onUnmounted, ref, useTemplateRef, watchEffect} from "vue";
import {storeToRefs} from "pinia";
import {useSocketFetch} from "@/composables/useSocketFetch";
import {useGameStore, useMainStore} from "@/stores";
import {GameStatus} from "@/constant/gameData.ts";
import emitter from "@/utils/emitter";

import HistoryTop from "@/components/desktop/HistoryTop.vue";
import HistoryOdd from "@/components/desktop/HistoryOdd.vue";
import GLoading from "@/components/ui/GLoading/index.vue"

const { toggleXHistoryShow } = useMainStore();
const { isShowXBetPanel, isShowXHistory } = storeToRefs(useMainStore());
const { setHistoryBallRate, setHistoryTrend } = useGameStore();
const { gameStatus } = storeToRefs(useGameStore())

const historyPanelRef = useTemplateRef('historyPanelRef');
const isActive = ref(true);
const loading = ref(false);
const isExpand = ref<boolean>(!isShowXBetPanel.value);

const handleTabChange = (b: boolean) => {
  isActive.value = b;
}

const updateIsExpand = () => {
  isExpand.value = !isShowXBetPanel.value;
}

const fetchData = async () => {
  loading.value = true;
  try {
    const { promise } = useSocketFetch('get-game-history-req', 'get-game-history-res');
    const res = await promise;
    const { data, code } = res?.data || {};
    if (code === 200) {
      setHistoryBallRate(data?.rateMap ?? {});
      setHistoryTrend(data?.historyResultVOList ?? []);
    }
  } catch (e) {
    console.error('fetch data error', e);
  } finally {
    loading.value = false;
  }
}

watchEffect(() => {
  if (gameStatus.value === GameStatus.GAME_SETTLEMENT) {
    toggleXHistoryShow(false);
  }
});

watchEffect(() => {
  if (!isShowXBetPanel.value) {
    // 如果是关闭投注面板，立马更新isExpand为true
    isExpand.value = true;
  }
});

watchEffect(() => {
  if (isShowXHistory.value) {
    isActive.value = true;
    fetchData();
  }
});

onMounted(() => {
  emitter.on('bet-panel-switched', updateIsExpand);
});

onUnmounted(() => {
  emitter.off('bet-panel-switched', updateIsExpand);
});
</script>

<style lang="scss" scoped>
.x-history-panel {
  --max-padding-b: 32px;
  --min-padding-b: 24px;

  &.has-full {
    padding-bottom: var(--max-padding-b);
  }

  padding-bottom: var(--min-padding-b);
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0;
  overflow: hidden;
  animation: modal-inner-show 0.3s ease-out forwards;

  &.hide {
    animation: modal-inner-hide 0.3s ease-out forwards;
  }
}

.tabs-item {
  cursor: pointer;
  text-align: center;
  min-width: 80px;
  padding-inline: 4px;

  &.active {
    background: linear-gradient(180deg, #72001c 0%, #910024 70%, #d50035 100%);
    font-weight: 700;
  }
}
</style>
