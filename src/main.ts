import { createApp } from "vue";
import { createPinia } from "pinia";
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';
import { register } from 'swiper/element/bundle';
import i18n from '@/i18n/index';
import router from "./router";

import '@/assets/style/style.css'

import App from "./App.vue";

const app = createApp(App);

const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);

// 注册 Swiper 组件
register();

app.use(pinia);
app.use(router);
app.use(i18n);
app.mount("#app");
