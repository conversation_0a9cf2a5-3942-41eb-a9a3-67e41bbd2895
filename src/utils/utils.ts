import CryptoJS from 'crypto-js';
import {utils} from "animejs";
import {storeToRefs} from "pinia";
import {useMainStore} from "@/stores";


export const glog = (...args: any[]) => {
  console.log(
    `%c[glog][${getTimeText()}] ===>%c`, 
    'background-color: #333; color: #4CAF50; font-weight: bold; padding: 2px 4px; border-radius: 2px;',
    'background-color: #333; color: white; padding: 2px 4px; border-radius: 2px;',
    ...args
  );
}

export const getTimeText = (time?: number) => {
  const d =  time ? new Date(time) : new Date();
  const pad2 = utils.padStart(2, '0');
  const pad3 = utils.padStart(3, '0');
  const h = pad2(d.getHours());
  const m = pad2(d.getMinutes());
  const s = pad2(d.getSeconds());
  const ms = pad3(d.getMilliseconds());
  return `${h}:${m}:${s}.${ms}`;
}

export const getCountDownText = (ms: number) => {
  if (ms <= 0) return "00:00:00";

  const pad2 = utils.padStart(2, '0');

  const m = Math.ceil(ms / 1000);
  const hours = Math.floor(m / 3600);
  const minutes = Math.floor(m % 3600 / 60);
  const seconds = Math.floor(m % 60);

  return `${ pad2(hours) }:${ pad2(minutes) }:${ pad2(seconds) }`;
}

/**
 * 转换数值为 1k, 2.5k, 1m 等格式的函数
 * @param num
 */
export const formatNumber = (num: number): string => {
  if (num >= 1000 ** 3) {
    return `${toFixedNumber(num / 1000 ** 3, 2)}B`;
  }
  if (num >= 1000 ** 2) {
    return `${toFixedNumber(num / 1000 ** 2, 2)}M`;
  }
  if (num >= 1000) {
    return `${toFixedNumber(num / 1000, 2)}K`;
  }
  return toFixedNumber(num, 2);
};

// 格式化当前余额
export const formatBalance = (num: number): string => {
  if (num > 1000 ** 3) {
    return `${toFixedNumber(num / 1000 ** 3, 2)}B`;
  }
  if (num > 1000 ** 2) {
    return `${toFixedNumber(num / 1000 ** 2, 2)}M`;
  }
  if (num > 1000) {
    return `${toFixedNumber(num / 1000, 2)}K`;
  }
  return toFixedNumber(num, 2);
};

export const roundNumber = (num: number) => {
  return Math.round(num * 1e4) / 1e4;
}

/**
 * 保留小数位，但去掉尾部的0和小数点
 * @param num 数字
 * @param digits 保留位数
 */
export const toFixedNumber = (num: number, digits: number): string => {
  return num.toFixed(digits).replace(/(?<=\.\d*)0*$/, '').replace(/\.$/, '');
}

export const isTouchDevice = () => {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
}

export const isMobileDevice = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  const isMobileUA = /mobile|android|iphone|ipod|blackberry|windows phone/i.test(userAgent);
  const isSmallScreen = window.innerWidth <= 768 || window.screen.width <= 768;
  return isMobileUA || isSmallScreen || isTouchDevice();
}

export const getClientX = (evt: TouchEvent | MouseEvent) => {
  const { isRotation } = storeToRefs(useMainStore());
  const evtField = isRotation.value ? 'clientY' : 'clientX'
  if (evt.type.startsWith('touch')) {
    return (evt as TouchEvent).touches[0]?.[evtField];
  }
  return (evt as MouseEvent)?.[evtField];
}

export const getTargetViewBoundingClientRect = (target: Element, isScale = true) => {
  const { scale, isRotation } = storeToRefs(useMainStore());
  const targetRect = target.getBoundingClientRect();

  const s = isScale ? scale.value : 1;

  const width = roundNumber(targetRect.width / s);
  const height = roundNumber(targetRect.height / s);
  const left = roundNumber(targetRect.left / s);
  const top = roundNumber(targetRect.top / s);
  const bottom = roundNumber(targetRect.bottom / s);
  const right = roundNumber(targetRect.right / s);

  if (!isRotation.value) return { width, height, left, top, bottom, right };

  return {
    width: height,                     // 宽度变成原来的高度
    height: width,                     // 高度变成原来的宽度
    left: top,                         // 新的left = 原来的top
    top: width - right,                // 新的top = 容器宽度 - 原来的right
    bottom: width - left,              // 新的bottom = 容器宽度 - 原来的left
    right: bottom,                     // 新的right = 原来的bottom
  }
}

export const getRootViewBoundingClientRect = (isScale = true) => {
  const root = document.querySelector('#view-box');
  if (!root) return { width: 0, height: 0, left: 0, top: 0, bottom: 0, right: 0 };
  return getTargetViewBoundingClientRect(root, isScale);
}

export const getBoundingClientRect = (target: Element, isScale = true) => {
  const root = document.querySelector('#view-box');
  if (!root) return { width: 0, height: 0, left: 0, top: 0, bottom: 0, right: 0 };
  const { scale, isRotation } = storeToRefs(useMainStore());
  const rootRect = root.getBoundingClientRect();
  const targetRect = target.getBoundingClientRect();

  const s = isScale ? scale.value : 1;

  const width = roundNumber(targetRect.width / s);
  const height = roundNumber(targetRect.height / s);
  const left = roundNumber((targetRect.left - rootRect.left) / s);
  const top = roundNumber((targetRect.top - rootRect.top) / s);
  const bottom = roundNumber((targetRect.bottom - rootRect.top) / s);
  const right = roundNumber((targetRect.right - rootRect.left) / s);

  if (!isRotation.value) return { width, height, left, top, bottom, right };

  // 向右旋转90度的坐标转换
  // 当界面向右旋转90度时，坐标系统发生如下变化：
  // - 原来的X轴变成新的Y轴
  // - 原来的Y轴变成新的X轴（方向相反）
  const containerWidth = roundNumber(rootRect.width / s);

  return {
    width: height,                     // 宽度变成原来的高度
    height: width,                     // 高度变成原来的宽度
    left: top,                         // 新的left = 原来的top
    top: containerWidth - right,       // 新的top = 容器宽度 - 原来的right
    bottom: containerWidth - left,     // 新的bottom = 容器宽度 - 原来的left
    right: bottom,                     // 新的right = 原来的bottom
  }
}

export const openFullscreen = () => {
  const view = document.querySelector("#view-box");
  return view?.requestFullscreen?.();
}

export const closeFullscreen = () => {
  return document.exitFullscreen();
}

export const rotate90Swiper = (swiper: any, isRotated: boolean) => {
  swiper.___touches = {};
  swiper.___touches.currentX = swiper.touches.currentX;
  swiper.___touches.currentY = swiper.touches.currentY;

  Object.defineProperty(swiper.touches, 'currentX', {
    set: function (v) {
      if (!isRotated) {
        swiper.___touches.currentX = v;
      } else {
        swiper.___touches.currentY = v; // 旋转时将 X 坐标映射到 Y
      }
    },
    get: function () {
      return swiper.___touches.currentX;
    },
  });

  Object.defineProperty(swiper.touches, 'currentY', {
    set: function (v) {
      if (!isRotated) {
        swiper.___touches.currentY = v;
      } else {
        swiper.___touches.currentX = v; // 旋转时将 Y 坐标映射到 X
      }
    },
    get: function () {
      return swiper.___touches.currentY;
    },
  });
}

export const getKey = (keyStr: string) => {
  // 1. 计算 MD5 哈希
  const md5Hash = CryptoJS.MD5(keyStr).toString(); // 得到 32 位 hex 字符串

  // 2. 截取前 16 个字符作为 key 和 iv
  const keyPart = md5Hash.substring(0, 16);
  const ivPart = md5Hash.substring(0, 16);

  // 3. 转换为 CryptoJS WordArray
  const key = CryptoJS.enc.Utf8.parse(keyPart);
  const iv = CryptoJS.enc.Utf8.parse(ivPart);

  return { key, iv };
}

export const decrypt = (k: string, value: string) => {
  const { key, iv } = getKey(k);

  // 4. Base64 解码加密数据 (CryptoJS expects Base64 input directly)
  const cipherWordArray = CryptoJS.enc.Base64.parse(value);

  // 5. 执行解密
  const decrypted = CryptoJS.AES.decrypt(
    { ciphertext: cipherWordArray } as CryptoJS.lib.CipherParams,
    key,
    { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 }
  );

  // 6. 转换为明文字符串
  try {
    return JSON.parse(decrypted.toString(CryptoJS.enc.Utf8));
  } catch (err) {
    console.error(err);
    return {}
  }
}

export const encrypt = (k: string, value: any) => {
  const { key, iv } = getKey(k);

  try {
    const encrypted = CryptoJS.AES.encrypt(
      JSON.stringify(value),
      key,
      { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 }
    );

    return encrypted.toString();
  } catch (e) {
    console.error(e);
    return "";
  }
}
