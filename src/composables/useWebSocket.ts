import {onUnmounted, ref} from 'vue';
import {useI18n} from "vue-i18n";
import {md5} from 'js-md5';
import {v4 as uuidv4} from 'uuid';
import * as protobuf from 'protobufjs';
import {SocketEventCode} from '@/constant/gameData.ts';
import {decrypt, encrypt, getTimeText, glog} from '@/utils/utils';
import {openMessage} from "@/components/ui/GMessage/index.ts";
import emitter from '@/utils/emitter';

const SERVER_NAME = 'game-live-pingballs';
const GAME_ID = "100201";

const HEARTBEAT_TIME = 4e4;

// 定义 Protocol Buffer 消息结构
let MessageDTO: protobuf.Type;

// 初始化 protobuf
const initProtobuf = async () => {
  try {
    // 使用 Vite 的动态导入功能加载 proto 文件
    const protoDefinition = await import('@/proto/message.proto?raw');
    const root = await protobuf.parse(protoDefinition.default).root;
    MessageDTO = root.lookupType('MessageDTO');
  } catch (error) {
    console.error('Failed to load protobuf definition:', error);
  }
};

export function useWebSocket(url: string, callback: (ws: WebSocket) => void) {
  const { t } = useI18n();

  const socket = ref<WebSocket | null>(null)
  const isConnected = ref(false)
  const reconnectAttempts = ref(0)

  const maxReconnectAttempts = 5
  const handlers = new Map<SocketEventCode, MessageHandler>()

  // 是否开始重连
  let isStartReconnect = false;
  // 是否允许重连
  let isEnableReconnect = true;
  // 重连定时器
  let reconnectTimer: any = null;
  // 心跳定时器
  let heartTimer: any = null;
  // 是否关闭当前页面或刷新当前页面
  let isClosedOrRefresh = false;

  // 初始化连接
  const connect = async () => {
    // 确保在使用前初始化
    await initProtobuf();

    isEnableReconnect = true;

    socket.value = new WebSocket(url)

    socket.value.binaryType = 'arraybuffer';

    socket.value.onopen = () => {
      isConnected.value = true;
      isStartReconnect = false;
      reconnectAttempts.value = 0;
      sendHeartbeat();
      callback(socket.value!);
      glog('WebSocket connected');
    }

    socket.value.onmessage = (event) => {
      try {
        const buffer = new Uint8Array(event.data);
        const message = MessageDTO.decode(buffer);
        const { messageId, data } = message as unknown as SocketResData;

        if (!messageId || !data) {
          console.error('Invalid message format, messageId or data is empty');
          return;
        }

        const messageData: SocketData = decrypt(messageId, data);

        // 处理心跳
        if (messageData?.path === SocketEventCode.PING) {
          send(SocketEventCode.PONG);
          return;
        }

        // 防止出现其他游戏推送
        if (messageData?.gameId && messageData?.gameId !== GAME_ID) {
          console.warn('other game push', messageData?.gameId);
          return;
        }

        if (messageData?.path !== SocketEventCode.BET_AMOUNT) {
          glog(`[${getTimeText(messageData.currentTimeMillis)}]WebSocket message received`, messageData.path, messageData)
        }

        const handler = handlers.get(messageData?.path as SocketEventCode);
        handler?.(messageData);
      } catch (error) {
        console.error('WebSocket message parsing error:', error)
      }
    }

    socket.value.onclose = (event) => {
      console.log('WebSocket disconnected')
      console.log("Code:", event.code);
      console.log("Reason:", event.reason);
      console.log("WasClean:", event.wasClean);
      isConnected.value = false;
      // 如果是客户端手动关闭，则不进行重连
      if (event.code === 4000 && event.reason === 'refresh_or_close') {
        return;
      }
      if (isClosedOrRefresh) {
        return;
      }
      !isStartReconnect && openMessage({ message: t('network_error') });
      if (isEnableReconnect) {
        attemptReconnect()
      }
    }

    socket.value.onerror = (error) => {
      console.error('WebSocket error:', error)
    }
  }

  // 重连逻辑
  const attemptReconnect = () => {
    if (reconnectAttempts.value < maxReconnectAttempts) {
      isStartReconnect = true

      reconnectAttempts.value++

      // const delay = Math.min(1000 * reconnectAttempts.value, 10000) // 指数退避
      const delay = 2000 // 指数退避 固定每2s重新连接

      glog(`Attempting to reconnect in ${delay}ms...`)

      reconnectTimer = setTimeout(connect, delay)
    } else {
      // 重连失败
      glog('Max reconnection attempts reached');
      isStartReconnect = false;
      emitter.emit('socket-reconnect-fail');
    }
  }

  // 固定间隔发送心跳
  const sendHeartbeat = () => {
    if (isConnected.value && socket.value) {
      send(SocketEventCode.PONG);
    }
    heartTimer = setTimeout(sendHeartbeat, HEARTBEAT_TIME);
  }

  // 发送消息
  const send = (path: SocketEventCode, data?: any) => {
    if (isConnected.value && socket.value) {
      const msgId = md5(`${uuidv4()}_${path}_${Date.now()}`);
      const sendData = { path, msgId, data, serverName: SERVER_NAME };
      const sendBody = { messageId: msgId, path, data: encrypt(msgId, sendData) };
      const payload = MessageDTO.create(sendBody);
      socket.value.send(MessageDTO.encode(payload).finish());
      glog('WebSocket send', path, data, sendBody);
      return msgId;
    } else {
      console.log('WebSocket is not connected', path, data);
    }
  }

  // 订阅消息
  const subscribe = (eventCode: SocketEventCode, handler: MessageHandler) => {
    handlers.set(eventCode, handler)
  }

  // 取消订阅
  const unsubscribe = (eventCode: SocketEventCode) => {
    handlers.delete(eventCode)
  }

  // 手动关闭连接(不进行重连)
  const disconnect = (code?: number, reason?: string) => {
    isEnableReconnect = false;

    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }

    if (heartTimer) {
      clearTimeout(heartTimer);
      heartTimer = null;
    }

    if (socket.value) {
      socket.value.close(code, reason);
      socket.value = null;
    }
  }

  // 自动清理
  onUnmounted(() => {
    isClosedOrRefresh = true;
    console.log("======unmount close socket");
    disconnect(4000, "refresh_or_close");
  });

  window.addEventListener('beforeunload', () => {
    isClosedOrRefresh = true;
    console.log("======beforeunload close socket");
    disconnect(4000, "refresh_or_close");
  });

  return {
    socket,
    isConnected,
    connect,
    disconnect,
    send,
    subscribe,
    unsubscribe
  }
}
