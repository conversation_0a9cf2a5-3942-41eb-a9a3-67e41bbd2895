declare global {
  interface Window {
  }
}

declare interface SocketResData {
  messageId: string;
  data: string;
  path: string;
}

declare interface SocketData {
  currentTimeMillis: number;
  data: any;
  msgId: string;
  path: string;
  serverName: string;
}

declare interface EmitterCallbackData<T, K> {
  data?: T;
  callback?: (data: K) => void;
}

declare interface BetItemRes {
  betAmount: number;
  betLocation: string;
  count: number;
  currency: string;
  playTypeId: string;
  winOrLose?: number;
  awardAmount?: number;
}

declare interface BetItemData {
  type: GamePlayType;
  location: string;
  amount: string;
  odds: string;
  bets: number;
  count: number;
  title?: string;
  image?: string;
}

declare interface VideoStream {
  title: string;
  url: string;
  resolution: number;
}

declare interface VideoBlock {
  [key in number]: { a?: VideoStream; b?: VideoStream; }
}

declare interface VideoStreamData {
  domestic: VideoBlock;
  international: VideoBlock;
}

declare interface BetChipsResult {
  betList?: string;
  originBetList?: string;
}

declare interface WinLocation {
  type1Loc?: number;
  type2Loc?: number;
  type3Loc?: number;
  type4Loc?: number;
}

declare interface GameTypeData {
  betTitle: any,
  playTypeId: string,
  playTypeName: string,
  rate: any,
  sort: number,
  status: boolean,
}

declare interface HistoryTrend {
  roundId: string;
  result: string;
  even: boolean;
  num: string;
}


declare type MessageHandler = (data: SocketData) => void;


declare interface SoundConfig {
  // 音频的唯一标识符，用于在音频管理器中引用特定的音频
  id: string;

  // 音频文件的路径或URL（例如 'background_music.mp3'），支持MP3、OGG等格式
  src: string;

  // 是否循环播放音频，默认为 false。常用于背景音乐（true）或一次性音效（false）
  loop?: boolean;

  // 音频的初始音量，范围 0.0 到 1.0，默认为 1.0。用于设置默认播放音量
  volume?: number;

  // 音频池大小，指定并发播放的音频实例数，默认为 1。适用于频繁触发的音效（如跳跃音效）
  poolSize?: number;
}