import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import tailwindcss from "@tailwindcss/vite";
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue({
      template: {
        compilerOptions: {
          isCustomElement: (tag) => tag.includes('swiper'),
        }
      }
    }),
    vueJsx(),
    tailwindcss(),
    vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  build: {
    minify: 'terser', // 确保使用 Terser 进行压缩
    terserOptions: {
      compress: {
        // drop_console: true, // 移除 console 语句
        drop_debugger: true, // 移除 debugger 语句
      },
      format: {
        comments: false, // 移除注释
      },
    },
  },
})
