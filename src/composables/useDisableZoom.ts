import {onMounted, onUnmounted} from "vue";

export const useDisableZoom = () => {
  const iosGestureStartHandle = (e: any) => {
    e.preventDefault();
  }

  const disableMultiTouch = (e: TouchEvent) => {
    if (e.touches.length > 1) {
      // 阻止多指缩放
      e.preventDefault();
      e.stopPropagation();
    }
  }

  const disableDoubleTap = (e: MouseEvent) => {
    // 阻止双击放大
    e.preventDefault();
  }

  onMounted(() => {
    document.addEventListener('touchstart', disableMultiTouch);
    document.addEventListener('touchmove', disableMultiTouch);
    document.addEventListener('dblclick', disableDoubleTap);
    document.addEventListener('gesturestart', iosGestureStartHandle);
    document.addEventListener('gesturechange', iosGestureStartHandle);
    document.addEventListener('gestureend', iosGestureStartHandle);
  })

  onUnmounted(() => {
    document.removeEventListener('touchstart', disableMultiTouch);
    document.removeEventListener('touchmove', disableMultiTouch);
    document.removeEventListener('dblclick', disableDoubleTap);
    document.removeEventListener('gesturestart', iosGestureStartHandle);
    document.removeEventListener('gesturechange', iosGestureStartHandle);
    document.removeEventListener('gestureend', iosGestureStartHandle);
  })
}