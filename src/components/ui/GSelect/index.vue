<script setup lang="ts">
import {computed, nextTick, onMounted, onUnmounted, ref, useTemplateRef} from "vue";
import {getBoundingClientRect, getRootViewBoundingClientRect} from "@/utils/utils.ts";

const props = defineProps<{
  value?: number | string;
  options: { label: string; value: number | string }[];
}>();

const emits = defineEmits<{
  (e: 'change', value: string | number): void
}>()

const mValue = defineModel<number | string>();

const show = ref(false);

const placement = ref<'top' | 'bottom'>('bottom');

const selectTargetRef = useTemplateRef('selectTargetRef')
const selectOptionsRef = useTemplateRef('selectOptionsRef')

const active = computed(() => {
  return props.options.find(item => {
    return item.value === (mValue.value ?? props.value)
  });
});

const updateValue = (val: string | number) => {
  mValue.value = val;
  show.value = false;
  emits('change', val);
}

const handleHide = (evt: MouseEvent) => {
  if (evt.target === selectTargetRef.value) return;
  show.value = false;
}

const handleClick = async () => {
  show.value = !show.value;
  if (show.value) {
    await nextTick();
    const { height: rootHeight = 0 } = getRootViewBoundingClientRect();
    const { height: optionsHeight = 0 } = getBoundingClientRect(selectOptionsRef.value as Element);
    const { left = 0, top = 0, bottom = 0, width = 0, height = 0 } = getBoundingClientRect(selectTargetRef.value as Element);
    const offsetH = rootHeight - bottom;
    if (optionsHeight > offsetH) {
      selectOptionsRef.value?.style.setProperty('--inset', `auto auto ${rootHeight - top}px ${left}px`);
      placement.value = 'top';
    } else {
      selectOptionsRef.value?.style.setProperty('--inset', `${top + height}px auto auto ${left}px`);
      placement.value = 'bottom';
    }
    selectOptionsRef.value?.style.setProperty('--w', width + 'px');
  }
}

onMounted(() => {
  document.body.addEventListener('click', handleHide);
});

onUnmounted(() => {
  document.body.removeEventListener('click', handleHide);
});
</script>

<template>
  <div ref="selectRef" class="g-select" :class="{ 'open': show }">
    <button ref="selectTargetRef" class="g-select--value" :class="`placement-${placement}`" @click.stop="handleClick">
      {{ active?.label }}
      <svg xmlns="http://www.w3.org/2000/svg" width="11" height="6" viewBox="0 0 11 6" fill="none" class="absolute right-2 inset-y-0 m-auto" :style="{ transform: show ? 'rotate(180deg)' : 'rotate(0deg)' }">
        <path d="M10 1L5.5 5L1 1" stroke="white" stroke-linecap="round" stroke-linejoin="round" />
      </svg>
    </button>
    <Teleport to="#view-box">
      <div v-show="show" ref="selectOptionsRef" class="g-select--options scrollbar-hidden" :class="`placement-${placement}`">
        <div
          v-for="item in options"
          :key="item.value"
          @click.stop="updateValue(item.value)"
          :class="{
            'option-item': true,
            'option-selected': item.value === active?.value,
          }"
        >
          {{ item.label }}
        </div>
      </div>
    </Teleport>
  </div>
</template>

<style lang="scss" scoped>
.g-select {
  position: relative;
}

.g-select.open .g-select--value.placement-top {
  border-radius: 0 0 4px 4px;
}

.g-select.open .g-select--value.placement-bottom {
  border-radius: 4px 4px 0 0;
}

.g-select--value {
  position: relative;
  min-width: 88px;
  height: 24px;
  background-color: #242328;
  color: #fff;
  border: 1px solid #302f32;
  border-radius: 4px;
  padding: 4px 27px 4px 8px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 400;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.g-select--options {
  position: absolute;
  inset: var(--inset);
  width: var(--w);
  max-height: 150px;
  background-color: #242328;
  border: 1px solid #302f32;
  overflow-y: auto;
  z-index: 999999;

  &.placement-top {
    border-radius: 4px 4px 0 0;
  }

  &.placement-bottom {
    border-radius: 0 0 4px 4px;
  }

  .option-item {
    padding: 4px 8px;
    cursor: pointer;
    font-size: 12px;
    color: #fff;
    font-weight: 400;

    &:hover {
      background-color: #302f32;
    }

    &.option-selected {
      color: #ff003f;
    }
  }
}
</style>