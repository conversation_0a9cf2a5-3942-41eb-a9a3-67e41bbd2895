<script setup lang="ts">
  import {computed, onMounted, onUnmounted, useTemplateRef} from "vue";
  import {getClientX, getTargetViewBoundingClientRect, roundNumber} from "@/utils/utils";
  
  const props = withDefaults(defineProps<{
    min?: number;
    max?: number;
    runwaySize?: number;
    thumbSize?: number;
    runwayColor?: string;
    barColor?: string;
    thumbColor?: string;
  }>(), {
    min: 0,
    max: 1,
    runwayColor: '#585858',
    barColor: '#FF003F',
    thumbColor: '#FFEEF3',
    runwaySize: 4,
    thumbSize: 12,
  });

  const mValue = defineModel<number>();

  const sliderRef = useTemplateRef('sliderRef');
  const sliderThumbRef = useTemplateRef('sliderThumbRef');

  let isMoveing = false;
  let startX = 0;
  let endX = 0;

  const thumbX = computed(() => {
    const { width = 0 } = getTargetViewBoundingClientRect(sliderRef.value as Element) || {};
    const min = (props.thumbSize / 2 / width);
    const max = (width - props.thumbSize / 2) / width;
    return Math.max(Math.min(mValue.value ?? 0, max), min);
  });

  const handleClick = (e: TouchEvent | MouseEvent) => {
    const targetRect = getTargetViewBoundingClientRect(sliderRef.value as Element, false);
    const evtLeft = getClientX(e);
    const value = (evtLeft - targetRect.left) / targetRect.width;
    mValue.value = roundNumber(Math.min(Math.max(value, props.min), props.max));
  }

  const handleTouch = (e: TouchEvent | MouseEvent) => {
    isMoveing = true;
    e.stopPropagation();
    startX = getClientX(e);
    endX = getClientX(e);
  }

  const handleTouchMove = (e: TouchEvent | MouseEvent) => {
    if (!isMoveing) return;

    endX = getClientX(e);

    const { width = 0 } = getTargetViewBoundingClientRect(sliderRef.value as Element, false) || {};
    const distance = endX - startX;
    const value = (mValue.value ?? 0) + distance / width;

    mValue.value = roundNumber(Math.min(Math.max(value, props.min), props.max));

    startX = endX;
  }

  const handleTouchEnd = () => {
    isMoveing = false;
  }

  onMounted(() => {
    sliderRef.value?.addEventListener('touchstart', handleClick);
    sliderRef.value?.addEventListener('mousedown', handleClick);
    sliderThumbRef.value?.addEventListener('touchstart', handleTouch);
    sliderThumbRef.value?.addEventListener('mousedown', handleTouch);
    document.body.addEventListener('touchmove', handleTouchMove);
    document.body.addEventListener('touchend', handleTouchEnd);
    document.body.addEventListener('mousemove', handleTouchMove);
    document.body.addEventListener('mouseup', handleTouchEnd);
  });

  onUnmounted(() => {
      document.body.removeEventListener('touchmove', handleTouchMove);
      document.body.removeEventListener('touchend', handleTouchEnd);
      document.body.removeEventListener('mousemove', handleTouchMove);
      document.body.removeEventListener('mouseup', handleTouchEnd);
  });
</script>

<template>
  <div ref="sliderRef" class="g-slider">
    <div class="g-slider--runway">
      <div ref="sliderBarRef" class="g-slider--bar"></div>
      <div ref="sliderThumbRef" class="g-slider--thumb"></div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.g-slider {
  width: 100%;
  height: calc(v-bind(thumbSize) * 1px);
  display: flex;
  align-items: center;

  .g-slider--runway {
    position: relative;
    cursor: pointer;
    width: 100%;
    height: calc(v-bind(runwaySize) * 1px);
    background-color: v-bind(runwayColor);
    border-radius: calc(v-bind(runwaySize) / 2 * 1px);
  }

  .g-slider--bar {
    width: calc(v-bind(mValue) * 100%);
    height: calc(v-bind(runwaySize) * 1px);
    border-radius: calc(v-bind(runwaySize) / 2 * 1px);
    background-color: v-bind(barColor);
  }

  .g-slider--thumb {
    position: absolute;
    left: calc(v-bind(thumbX) * 100%);
    top: 50%;
    transform: translate3d(-50%, -50%, 0);
    cursor: pointer;
    width: calc(v-bind(thumbSize) * 1px);
    height: calc(v-bind(thumbSize) * 1px);
    background-color: v-bind(thumbColor);
    border-radius: 50%;
    box-shadow: 0 0 4px 1px #ff003f;
  }
}
</style>