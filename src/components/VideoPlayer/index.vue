<script setup lang="ts">
import {onMounted, ref, watchEffect} from "vue";
import {storeToRefs} from "pinia";
import {useMainStore} from "@/stores";
import {CameraType} from "@/constant/gameData.ts";
import {rotate90Swiper} from "@/utils/utils";

import GLoading from "@/components/ui/GLoading/index.vue";
import JessibucaPlayer from "./JessibucaPlayer.vue";

const { cameraInfo, isRotation, isShowVideoLoading } = storeToRefs(useMainStore());
const { toggleCameraType } = useMainStore();

const swiperRef = ref<any>(null);

onMounted(() => {
  watchEffect(() => {
    if (cameraInfo.value.type === CameraType.A) {
      swiperRef.value?.swiper?.slideTo(0);
    }
    if (cameraInfo.value.type === CameraType.B) {
      swiperRef.value?.swiper?.slideTo(1);
    }
  });

  if (isRotation.value) {
    // 初始化时应用旋转逻辑
    rotate90Swiper(swiperRef.value?.swiper, true);
  }

  swiperRef.value?.addEventListener('swiperslidechange', (event: CustomEvent) => {
    const active = event.detail?.[0]?.activeIndex;
    if (active === 0 && cameraInfo.value.type !== CameraType.A) {
      toggleCameraType(CameraType.A);
    }
    if (active === 1 && cameraInfo.value.type !== CameraType.B) {
      toggleCameraType(CameraType.B);
    }
  });
});
</script>

<template>
  <GLoading class="size-full" :show="isShowVideoLoading" show-network-speed>
    <div class="video-player-wrapper size-full">
      <div class="video-player-box size-full">
        <swiper-container ref="swiperRef" class="h-full">
          <swiper-slide>
            <JessibucaPlayer :type="CameraType.A" />
          </swiper-slide>
          <swiper-slide>
            <JessibucaPlayer :type="CameraType.B" />
          </swiper-slide>
        </swiper-container>
      </div>
    </div>
  </GLoading>
</template>

<style scoped>

</style>