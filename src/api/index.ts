import request from '../utils/request';

export const  getGameConfig = (data: { currency: string; gameId: string }) => {
  return request.get<any>('/game-gamr/game/config', data);
}

export const getUserBalance = () => {
  return request.post<any>('/game-gamr/game/balance');
}

//玩家投注
export const playerBet = (data: { betAmount?: number; location?: string; roundId?: string; sessionId?: string; type?: string }) => {
  return request.post<any>('/game-gamr/gamr/bet', data);
}

//获取历史投注信息
export const getBetHistory = (data: { gameId: string, page: number, size: number }) => {
  return request.get<any>('/game-gamr/game/recordList', data);
}

//获取历史期数信息
export const getRoundHistory = (data: { gameId: string, page: number, size: number }) => {
  return request.get<any>('/game-gamr/game/roundList', data);
}

// 模拟器登录
export const login = (data: { currency: string; lang: string; userId: string }) => {
  return request.post<any>('/simulator/simulator/login', data);
}

// 获取游戏token
export const getGameAddress = (data: any) => {
  return request.get<any>('/simulator/simulator/address', data);
}