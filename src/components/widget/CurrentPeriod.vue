<script setup lang="ts">
import { useGameStore } from '@/stores';
import { storeToRefs } from 'pinia';

const { gameRoundId } = storeToRefs(useGameStore());
</script>

<template>
<div class="current-period-widget">
  {{ $t('current_period') }}: {{ gameRoundId }}
</div>
</template>

<style lang="scss" scoped>
:where([data-layout="x"]) .current-period-widget {
  color: rgba(255, 255, 255, 0.8);
  line-height: 20px;
  padding-inline: 8px;
  background-image: url("/images/period-bg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: left bottom;
  overflow: hidden;
}

:where([data-layout="y"]) .current-period-widget {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
}
</style>