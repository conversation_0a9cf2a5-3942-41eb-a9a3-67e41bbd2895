<script setup lang="ts">
import { storeToRefs } from "pinia";
import { useGameStore } from "@/stores";
import { GameStatus } from "@/constant/gameData.ts";

const { gameStatus } = storeToRefs(useGameStore());
</script>

<template>
  <div v-if="gameStatus === GameStatus.GAME_SETTLEMENT" class="absolute top-0 left-0 w-full h-full flex items-center">
    <div class="win-flash">
      <img src="/images/win-flash.png" alt="WIN flash" class="win-flash-img"/>
    </div>
    <div class="win-text relative w-full flex justify-center">
      <img src="/images/win.png" alt="WIN"/>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.win-flash {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
}

.win-flash-img {
  animation: rotate-animation 3s infinite linear;
}

.win-text {
  width: 100%;
  height: 20px;
  overflow: hidden;
  background: linear-gradient(90deg, rgba(239, 231, 12, 0.00) 0%, #EFD50C 28.37%, #EFD50C 67.31%, rgba(239, 231, 12, 0.00) 100%);

  img {
    object-fit: contain;
    width: 30px;
    animation: dispaly-animation 1s linear;
  }
}

@keyframes dispaly-animation {
  0% {
    transform: scale(0);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes rotate-animation {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>