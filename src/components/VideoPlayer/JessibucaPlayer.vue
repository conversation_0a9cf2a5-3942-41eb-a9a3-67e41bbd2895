<script setup lang="ts">
import {computed, onMounted, onUnmounted, watch} from "vue";
import {storeToRefs} from "pinia";
import {debounce, sleep} from "radash";
import {useGameStore, useMainStore} from "@/stores";
import {CameraType} from "@/constant/gameData.ts";
import emitter from "@/utils/emitter.ts";

const props = defineProps<{
  type: CameraType;
}>()

const { cameraInfo, isSystemError } = storeToRefs(useMainStore());
const { useStreamList } = storeToRefs(useGameStore());
const { toggleVideoLoading } = useMainStore();

let jessibuca: Jessibuca | null = null;

const isCurrPlayer = computed(() => props.type === cameraInfo.value.type);

const playUrl = computed(() => {
  const { quality } = cameraInfo.value;
  const { a, b } = useStreamList.value?.[quality] || {};
  const line = props.type === CameraType.A ? a : b;
  return line?.url;
});

const setupJessibucaPlayer = async () => {
  await stopPlayer(true);

  const container = document.getElementById(`jessibuca-player-${props.type}`);

  if (!container) return console.error('not fount jessibuca player container');

  jessibuca = new Jessibuca({
    container,
    // debug: true,
    videoBuffer: 0.2,
    isResize: false,
    // isFullResize: true,
    keepScreenOn: true,
    // hiddenAutoPause: true,
    controlAutoHide: true,
  });

  jessibuca.toggleControlBar(false);

  jessibuca.on("start", function () {
    console.log("play start")
    toggleVideoLoading(false)
  });

  jessibuca.on("timeout", function (error) {
    console.log('timeout:', error)
  });

  jessibuca.on("loadingTimeout", function () {
    console.log('loadingTimeout')
  });

  jessibuca.on("delayTimeout", function () {
    console.log('delayTimeout')
  });

  jessibuca.on("error", function (error: any) {
    console.log('error:', error)
    if (error === Jessibuca.ERROR.fetchError) {
      //
    }
    if (error === Jessibuca.ERROR.webcodecsH265NotSupport) {
      //
    }
  });
}

const stopPlayer = async (isDestroy?: boolean) => {
  if (!jessibuca) return;
  
  await jessibuca.pause();

  if (isDestroy) {
    jessibuca.destroy();
    jessibuca = null;
  }
}

const startPlayer = async (isStop?: boolean) => {
  if (!isCurrPlayer.value) return;
  if (!jessibuca) return;
  if (!playUrl.value) return;
  if (document.visibilityState === 'hidden') return;

  toggleVideoLoading(true);

  isStop && await stopPlayer();
  await jessibuca.play(playUrl.value);
}

const handleVisible = () => {
  if (document.visibilityState === 'visible') {
    startPlayer();
  } else {
    stopPlayer();
  }
}

const handleReStart = () => startPlayer(true);

const handleUpdate = async () => {
  if (!isCurrPlayer.value) {
    stopPlayer();
    return;
  }

  toggleVideoLoading(true);

  await setupJessibucaPlayer();
  await sleep(100);
  await startPlayer();
}

const debounceUpdate = debounce({ delay: 300 }, handleUpdate);

onMounted(() => {
  emitter.on('show-switch-scenes', handleReStart);
  window.addEventListener('visibilitychange', handleVisible);

  watch(
    [cameraInfo, useStreamList],
    debounceUpdate,
    {
      deep: true,
      immediate: true,
    }
  );

  watch(isSystemError, (isError) => {
    if (!isCurrPlayer.value) return;
    if (isError) {
      stopPlayer();
    } else {
      startPlayer();
    }
  });
});

onUnmounted(() => {
  emitter.off('show-switch-scenes', handleReStart);
  window.removeEventListener('visibilitychange', handleVisible);

  stopPlayer(true);
});
</script>

<template>
  <div class="size-full jussibuca-player" :id="`jessibuca-player-${type}`"></div>
</template>

<style lang="scss" scoped>
.jussibuca-player {
  :deep(canvas) {
    width: 100% !important;
    height: 100% !important;
    inset: 0 !important;
    transform: scale(1) !important;
  }
  :deep(.jessibuca-loading) {
    display: none !important;
    opacity: 0 !important;
  }
}
</style>
