<template>
  <div class="mx-10">
    <table>
      <tbody>
        <tr v-for="i in lists" :key="i">
          <td v-for="(item, idx) in computedOddResult.slice(0, 28)" :key="`${item.even}_${item.count}_${idx}`">
            <template v-if="item.even">
              <img v-show="item.count >= i" src="/images/pc-even.png" alt="even" class="w-4 h-4 ml-[2px] mr-[2px]">
            </template>
            <template v-else>
              <img v-show="item.count >= i" src="/images/pc-odd.png" alt="odd" class="w-4 h-4 ml-[2px] mr-[2px]">
            </template>
          </td>
        </tr>
        <tr v-show="!isExpand">
          <td v-for="(item, idx) in computedOddResult.slice(0, 28)" :key="idx">
             {{ item.count > 3 ? `+${item.count - 3}` : '' }}
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { storeToRefs } from "pinia";
import { useGameStore } from "@/stores";

const props = defineProps<{
  isExpand: boolean;
}>();

const { historyTrend } = storeToRefs(useGameStore());

const lists = computed(() => {
  return props.isExpand ? 12 : 3;
});

// 处理单双数据的函数
const handleOdds = (data: Array<{ even: boolean }>) => {
  if (data.length === 0) return [];
  const result: Array<{id:number,even:boolean,count:number}> = [];
  let count = 0;
  for (let i = 0; i < data.length; i++) {
    if(result.length == 0){
      result.push({id:count,even:data[i].even,count:1});
    }else{
      if(result[result.length-1].even == data[i].even){
        result[result.length-1].count++;
      }else{
        count++;
        result.push({id:count,even:data[i].even,count:1});
      }
    }
  }
  return result;
};

const computedOddResult = computed(() => {
  return handleOdds(historyTrend.value);
});
</script>

<style scoped>
table {
  border-collapse: collapse;
  width: 100%;
  border: 1px solid rgba(255, 255, 255, 0.3); /* 添加表格边框 */
}

tr {
  border: 1px solid rgba(255, 255, 255, 0.3); /* 添加行边框 */
  height: 18px;
}

td img{
  width: 16px !important;
  height: 16px !important;
}

td {
  width: 18px;
  padding: 0 !important;
  height: auto !important;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.3); /* 添加单元格边框 */
  line-height: 18px;
  color: #ffffff;
  font-size: 10px;
  font-weight: 700;
}
</style>
