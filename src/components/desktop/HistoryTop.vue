<template>
  <div id="top" class="x-history-top">
    <div>
      <swiper-container
        ref="swiperRef"
        class="h-full"
        :navigation="{
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev',
        }"
        :pagination="{ el: '.swiper-pagination' }"
        :spaceBetween="30"
        @swiperslidechange="handleSlideChange"
      >
        <swiper-slide v-for="(slideItems, index) in groupedLists" :key="index">
          <section :class="{
            'grid grid-flow-col grid-cols-3 gap-x-9 size-full flex-wrap transition-all': true,
            'grid-rows-2 gap-y-1.5': !isExpand,
            'grid-rows-5 gap-y-3': isExpand,
          }">
            <div v-for="(item, idx) in slideItems" :key="idx">
              <div class="text-white text-default">NO.{{ item.roundId }}</div>
              <div class="flex align-center justify-between w-[178px] mt-1">
                <img
                  v-for="num in item.result.split(',')"
                  :key="num"
                  :src="`/images/ball-${num}.png`"
                  :alt="`ball ${num}`"
                  class="w-4 h-4"
                />
              </div>
            </div>
          </section>
        </swiper-slide>
      </swiper-container>
    </div>

    <div class="swiper-pagination h-5"></div>

    <div v-show="swiperIndex > 0" class="switch-btn swiper-button-prev left-1">
      <img src="/images/arrow-left.png" alt="arrow left" class="block size-full" />
    </div>

    <div v-show="swiperIndex < groupedLists.length - 1" class="switch-btn swiper-button-next right-1">
      <img src="/images/arrow-right.png" alt="arrow right" class="block size-full" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";
import { storeToRefs } from "pinia";
import { cluster } from "radash";
import type Swiper from "swiper";
import { useGameStore, useMainStore } from "@/stores";
import { rotate90Swiper } from "@/utils/utils";

const props = defineProps<{
  isExpand: boolean;
}>();

const { isShowXBetPanel, isRotation } = storeToRefs(useMainStore());
const { historyTrend } = storeToRefs(useGameStore())

const swiperRef = ref<any>(null);

const swiperIndex = ref(0);

const groupedLists = computed(() => {
  return cluster(historyTrend.value, props.isExpand ? 15 : 6);
});

const handleSlideChange = (evt: CustomEvent) => {
  const swiper = evt.detail?.[0] as Swiper;
  swiperIndex.value = swiper.realIndex;
}

watch(isShowXBetPanel, () => {
  swiperRef.value?.swiper?.slideTo(0);
});

onMounted(() => {
  if (isRotation.value) {
    // 初始化时应用旋转逻辑
    rotate90Swiper(swiperRef.value?.swiper, true);
  }
});
</script>

<style lang="scss" scoped>
.x-history-top {
  position: relative;
  height: 100%;
  padding-inline: 30px;
}

.switch-btn {
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  cursor: pointer;
  border-radius: 50%;
}

.swiper-pagination {
  display: flex;
  align-items: center;
  position: absolute;
  bottom: -5px;
  left: 47%;
}
</style>
