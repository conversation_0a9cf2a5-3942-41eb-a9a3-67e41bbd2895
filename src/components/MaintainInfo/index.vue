<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { useRouter } from 'vue-router';
import { useMainStore } from '@/stores';
import { SYSTEM_ERROR_CODE } from '@/constant';

const router = useRouter();
const { isHorizontal, systemError } = storeToRefs(useMainStore());

const handleBack = () => {
  router.back();
}

const handleReconnect = () => {
  window.location.reload();
}
</script>

<template>
  <div class="select-none fixed inset-0 size-full flex items-center justify-center bg-black/70 z-9999">
    <div class="maintain-info--inner w-70">
      <template v-if="systemError === SYSTEM_ERROR_CODE.SYSTEM_MAINTAIN">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
          <path d="M11.9499 3.30048C11.7266 3.77382 11.6099 4.29715 11.6099 4.83715C11.6099 5.80048 11.9833 6.70048 12.6633 7.38048C13.3399 8.05715 14.2433 8.43382 15.2066 8.43382C15.7466 8.43382 16.2699 8.31715 16.7433 8.09048C16.1033 8.73715 15.2433 9.08715 14.3399 9.08715C14.1033 9.08715 13.8666 9.06048 13.6366 9.01382C13.2666 8.93382 12.8733 9.05048 12.6033 9.32048L6.04994 15.8705C6.01661 14.8472 5.18994 14.0238 4.16661 13.9872L10.7133 7.43715C10.9833 7.16715 11.0999 6.78048 11.0199 6.40382C10.7833 5.27382 11.1266 4.11048 11.9466 3.30048L11.9499 3.30048ZM13.0933 13.1772L16.6933 16.7772C16.8933 16.9772 16.8933 17.3038 16.6933 17.5005C16.4966 17.6972 16.1666 17.6972 15.9699 17.5005L12.3699 13.9005L13.0933 13.1772ZM13.8866 1.23048C13.8033 1.23048 13.7166 1.24048 13.6299 1.25048C11.0799 1.65048 9.38328 4.10048 9.91661 6.64048L2.35994 14.2005C1.39661 15.1605 1.39661 16.7238 2.35994 17.6805C3.31661 18.6372 4.88661 18.6372 5.83994 17.6805L13.3999 10.1205C15.9399 10.6505 18.3933 8.96048 18.7866 6.40715C18.8833 5.79382 18.6366 5.44382 18.3133 5.44382C18.1633 5.44382 17.9999 5.51715 17.8399 5.67382L16.9399 6.57382C15.9833 7.53048 14.4133 7.53048 13.4566 6.57382C12.4933 5.61048 12.4933 4.05048 13.4566 3.09048C13.4566 3.09048 13.8599 2.68715 14.3566 2.19048C14.8066 1.74382 14.5833 1.22715 13.8866 1.23048ZM3.51994 16.5205C2.99994 16.0005 3.37994 15.1205 4.09994 15.1205C4.81994 15.1205 5.20661 16.0005 4.68328 16.5205C4.35994 16.8405 3.83994 16.8405 3.51994 16.5205ZM2.35327 1.99715L1.18994 3.15715L2.63994 5.19048L3.85661 5.83048L6.70328 8.67715L7.86328 7.51715L5.02328 4.66715L4.38327 3.45048L2.35327 1.99715ZM12.8033 11.8672L11.0633 13.6072C10.9033 13.7672 10.9033 14.0272 11.0633 14.1872L15.1733 18.2972C15.8133 18.9338 16.8566 18.9372 17.4966 18.2972C18.1366 17.6572 18.1366 16.6138 17.4966 15.9738L13.3866 11.8638C13.2233 11.7038 12.9666 11.7038 12.8033 11.8672Z" fill="white"/>
        </svg>
        <div class="text-white/70 text-xs px-3 text-center">{{ $t('the_system_is_under_maintenance') }}</div>
        <div :class="{ 'hidden': isHorizontal }" class="btn--wrapper">
          <button class="btn-item" @click="handleBack">{{ $t('return_to_lobby') }}</button>
        </div>
      </template>

      <template v-if="systemError === SYSTEM_ERROR_CODE.NETWORK_ERROR">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
          <path d="M19.7488 7.10981C19.5027 7.42231 19.0456 7.42231 18.7644 7.18012C9.42844 -1.00347 1.53391 6.83247 1.18235 7.18012C1.04172 7.31684 0.865942 7.38715 0.690161 7.38715C0.514379 7.38715 0.338598 7.31684 0.197973 7.18012C-0.0481206 6.93794 -0.0832769 6.48872 0.197973 6.20747C0.268286 6.102 9.35813 -2.91363 19.7097 6.13715C19.9948 6.37934 20.03 6.83247 19.7488 7.10981Z" fill="white"/>
          <path d="M16.9402 9.11622C17.1823 9.30372 17.237 9.61231 17.1355 9.87403C16.7839 9.78809 16.4167 9.74122 16.0417 9.74122C15.8777 9.74122 15.7175 9.74903 15.5613 9.76466C9.22141 4.667 4.19797 9.95606 3.99485 10.1943C3.85422 10.3662 3.64329 10.4365 3.46751 10.4365C3.32688 10.4365 3.1511 10.3662 3.01047 10.2647C2.72922 10.0225 2.72922 9.57325 2.97532 9.292C3.04563 9.22169 9.39719 2.49513 16.9402 9.11622ZM9.74875 10.3662C10.7527 10.3233 11.7331 10.6006 12.6667 11.1865C12.3386 11.5264 12.0691 11.917 11.8542 12.3428C11.1823 11.9365 10.5066 11.7334 9.81516 11.7529C8.06125 11.7881 6.76047 13.3115 6.76047 13.3115C6.51438 13.624 6.06125 13.6592 5.7761 13.417C5.45969 13.1748 5.42454 12.7608 5.67063 12.4443C5.74485 12.3428 7.36204 10.4717 9.74875 10.3662ZM9.95579 14.0772C10.323 14.0772 10.6902 14.2256 10.948 14.4834C11.2097 14.7412 11.3581 15.1006 11.3581 15.4639C11.3581 15.8272 11.2097 16.1865 10.948 16.4443C10.6863 16.7022 10.323 16.8506 9.95579 16.8506C9.5886 16.8506 9.22141 16.7022 8.9636 16.4443C8.70188 16.1865 8.55344 15.8272 8.55344 15.4639C8.55344 15.1006 8.70188 14.7412 8.9636 14.4834C9.22532 14.2256 9.5886 14.0772 9.95579 14.0772ZM16.1667 10.9561C14.2527 10.9561 12.698 12.5068 12.698 14.4209C12.698 16.335 14.2527 17.8897 16.1667 17.8897C18.0808 17.8897 19.6355 16.3389 19.6355 14.4248C19.6355 12.5108 18.0847 10.9561 16.1667 10.9561ZM17.9402 15.2568C18.0847 15.4014 17.9909 15.7256 17.7292 15.9873C17.4714 16.2451 17.1433 16.3389 16.9988 16.1983L16.1628 15.3623L15.3269 16.1983C15.1823 16.3428 14.8581 16.249 14.5964 15.9873C14.3386 15.7295 14.2448 15.4014 14.3855 15.2568L15.2214 14.4209L14.3855 13.585C14.2409 13.4404 14.3347 13.1162 14.5964 12.8545C14.8542 12.5967 15.1823 12.5029 15.3269 12.6436L16.1628 13.4795L16.9988 12.6436C17.1433 12.499 17.4675 12.5928 17.7292 12.8545C17.987 13.1123 18.0808 13.4404 17.9402 13.585L17.1042 14.4209L17.9402 15.2568Z" fill="white"/>
        </svg>
        <div class="text-white/70 text-xs px-3 text-center">{{ $t('network_error_reconnect') }}</div>
        <div class="btn--wrapper">
          <button class="btn-item" @click="handleReconnect">{{ $t('reconnect') }}</button>
        </div>
      </template>
    </div>
  </div>
</template>

<style lang="scss" scoped>
:where([data-layout="x"]) .maintain-info--inner {
  padding-block: 28px;
}

:where([data-layout="y"]) .maintain-info--inner {
  padding-block: 16px;
}

.maintain-info--inner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  border-radius: 8px;
  border: 1px solid rgba(188, 210, 219, 0.70);
  background-color: rgba(251, 251, 251, 0.10);
  backdrop-filter: blur(12px);

  .btn--wrapper {
    border-radius: 8px;
    padding: 1px;
    background-image: linear-gradient(to bottom, #FF3C6C, #5D0017);
    box-shadow: -1px -1px 1px 0 rgba(0, 0, 0, 0.25) inset, 1px 1px 1px 0 rgba(0, 0, 0, 0.25) inset;
  }

  .btn-item {
    cursor: pointer;
    color: #FFF;
    font-size: 14px;
    font-weight: 700;
    line-height: 11px;
    width: 160px;
    height: 40px;
    border-radius: 8px;
    background-image: linear-gradient(to bottom, #920D3B 0%, #4E142C 100%);
  }
}
</style>