<script setup lang="ts">
import { useMainStore } from "@/stores";
import { storeToRefs } from "pinia";
import { computed } from "vue";

const MEDAL_MAP = [ "gold", "sliver", "bronze" ];

const props = defineProps<{
  ball: string, // 当前球号码
  order: number // 当前球排名
}>();

const { isVertical } = storeToRefs(useMainStore());

const medal = computed(() => MEDAL_MAP[props.order - 1]);
const isGold = computed(() => props.order === 1);
</script>

<template>
  <div class="relative">
    <img :src="`/images/medal-${medal}.png`" :alt="medal" class="medal">
    <img v-if="isGold" src="@/assets/svg/medal-gold-bg.svg" :alt="`${medal} bg`" :class="{ 'w-[102px]': isVertical }">
    <img v-else-if="props.order === 2" src="@/assets/svg/medal-sliver-bg.svg" :alt="`${medal} bg`" :class="{ 'w-[78px]': isVertical }">
    <img v-else-if="props.order === 3" src="@/assets/svg/medal-bronze-bg.svg" :alt="`${medal} bg`" :class="{ 'w-[78px]': isVertical }">
    <img src="@/assets/svg/ball-halo.svg" :alt="`ball halo ${medal}`" :class="{ 'is-gold': isGold }" class="ball-halo"/>
    <img :src="`/images/ball-${ball}.png`" :alt="`ball ${medal}`" :class="{ 'is-gold': isGold }" class="ball"/>
  </div>
</template>

<style lang="scss" scoped>
:where([data-layout="x"]) {
  .medal { width: 19px; }

  .ball-halo.is-gold { width: 34px; }
  .ball-halo { width: 28px; }

  .ball.is-gold { width: 24px; }
  .ball { width: 20px; }
}

:where([data-layout="y"]) {
  .medal { width: 41px; }

  .ball-halo.is-gold { width: 50px; }
  .ball-halo { width: 38px; }

  .ball.is-gold { width: 36px; }
  .ball { width: 26px; }
}

.medal {
  position: absolute;
  left: 50%;
  top: 0;
  transform: translate(-50%, -60%);
}

.ball-halo {
  transform: translateX(-50%);
  position: absolute;
  left: 50%;
  top: 19%;
  &.is-gold { top: 32%; }
}

.ball {
  transform: translateX(-50%);
  position: absolute;
  left: 50%;
  top: 25%;
  &.is-gold { top: 40%; }
}
</style>