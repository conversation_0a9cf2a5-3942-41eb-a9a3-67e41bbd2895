<script setup lang="ts">
import {onMounted, onUnmounted, ref, watch} from "vue";
import {storeToRefs} from "pinia";
import {animate} from "animejs";
import {useMainStore, usePersistStore} from "@/stores";
import {formatAmount} from "@/utils/utils.ts";
import emitter from "@/utils/emitter";

const { isShowXBetPanel } = storeToRefs(useMainStore());
const { toggleXBetPanelShow, isVertical } = useMainStore();
const { playerBetTotal, betAmountInfo } = storeToRefs(usePersistStore());
const { setBetAmountInfo } = usePersistStore();

const polygonRef = ref();

const arrowExpandPath = '10.1449,0 0,8 7.05882,4.70588 10.1449,3.40164 12.9412,4.70588 20,8 10.1449,0';
const arrowClosePath = '10.1449,8 0,0 7.05882,3.29412 10.1449,4.59836 12.9412,3.29412 20,0 10.1449,8';

const updateBetAmount = (res: SocketData) => {
  const { data, code } = res.data || {};
  if (code === 200) {
    setBetAmountInfo(data);
  }
}

watch(isShowXBetPanel, (show) => {
  animate(polygonRef.value, {
    points: show ? arrowClosePath : arrowExpandPath,
    duration: 300,
    alternate: true,
    onComplete: self => self.cancel()
  });
});

onMounted(() => {
  emitter.on('game-bet-amount', updateBetAmount);
});

onUnmounted(() => {
  emitter.off('game-bet-amount', updateBetAmount);
});

</script>

<template>
  <div class="user-amount-inner">
    <img class="size-4 block mr-0.5" src="/images/total-bet-amount.png" alt="total bet amount">

    <div class="text-[#efe70c] font-bold">＄{{ formatAmount(playerBetTotal) }}</div>
    <div class="text-white/80 mx-1">/</div>
    <div class="text-white/80">＄{{ formatAmount(betAmountInfo.total ?? 0) }}</div>

    <div :class="{ 'hidden': isVertical }" class="ml-4 cursor-pointer" @click="toggleXBetPanelShow()">
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="8" viewBox="0 0 20 8" fill="none">
        <polygon ref="polygonRef" fill="white"
                 points="10.1449,0 0,8 7.05882,4.70588 10.1449,3.40164 12.9412,4.70588 20,8 10.1449,0"/>
      </svg>
    </div>
  </div>
</template>

<style scoped>
:where([data-layout="x"]) .user-amount-inner {
  min-width: 178px;
  background-image: url("/images/bet-amount-bg-x.png");
}

:where([data-layout="y"]) .user-amount-inner {
  min-width: 141px;
  background-image: url("/images/bet-amount-bg-y.png");
}

.user-amount-inner {
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  line-height: 20px;
  padding-inline: 16px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center bottom;
  transform: translate3d(0, 0, 0);
}
</style>