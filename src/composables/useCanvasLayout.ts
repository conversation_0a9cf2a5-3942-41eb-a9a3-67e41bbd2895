import {onMounted, onUnmounted, ref, watch, type Ref} from "vue";
import {debounce} from "radash";
import {storeToRefs} from "pinia";
import {useMainStore} from "@/stores";
import {roundNumber} from "@/utils/utils.ts";

import {
  VERTICAL_RATIO,
  DESKTOP_SCREEN_WIDTH,
  DESKTOP_SCREEN_HEIGHT,
  MOBILE_SCREEN_WIDTH,
  MOBILE_SCREEN_HEIGHT,
} from '@/constant';

export const useCanvasLayout = (rootRef: Ref<HTMLElement>) => {
  const scale = ref(1);
  const layout = ref<'x' | 'y'>('x');
  const viewSizeW = ref(DESKTOP_SCREEN_WIDTH);
  const viewSizeH = ref(DESKTOP_SCREEN_WIDTH);
  const mediaQuery = window.matchMedia('(orientation: landscape)');

  const { orientation, isRotation } = storeToRefs(useMainStore());
  const { setOrientation } = useMainStore();

  let resizeObserver: ResizeObserver | null = null;

  function handleOrientationChange(e: MediaQueryListEvent | MediaQueryList) {
    if (e.matches) {
      setOrientation('landscape');
    } else {
      setOrientation('portrait')
    }
  }

  const calcLayout = () => {
    const dvw = window.innerWidth;
    const dvh = window.innerHeight;
    // const dvw = document.body.clientWidth;
    // const dvh = document.body.clientHeight;
    // console.log(window.innerWidth, window.innerHeight, document.body.clientWidth, document.body.clientHeight)

    // 根据当前设备的方向来判断是否是横版布局
    const isLayoutX = dvw / dvh > VERTICAL_RATIO;
    let vwRate: number;
    let vhRate: number;

    if (isLayoutX) {
      if (isRotation.value) { // 旋转到竖版
        layout.value = 'y';
        viewSizeW.value = MOBILE_SCREEN_WIDTH;
        viewSizeH.value = MOBILE_SCREEN_HEIGHT;
        vwRate = dvw / viewSizeH.value;
        vhRate = dvh / viewSizeW.value;
      } else { // 横版
        layout.value = 'x';
        viewSizeW.value = DESKTOP_SCREEN_WIDTH;
        viewSizeH.value = DESKTOP_SCREEN_HEIGHT;
        vwRate = dvw / viewSizeW.value;
        vhRate = dvh / viewSizeH.value;
      }
    } else {
      if (isRotation.value) { // 旋转到横版
        layout.value = 'x';
        viewSizeW.value = DESKTOP_SCREEN_WIDTH;
        viewSizeH.value = DESKTOP_SCREEN_HEIGHT;
        vwRate = dvh / viewSizeW.value;
        vhRate = dvw / viewSizeH.value;
      } else { // 竖版
        layout.value = 'y';
        viewSizeW.value = MOBILE_SCREEN_WIDTH;
        viewSizeH.value = MOBILE_SCREEN_HEIGHT;
        vwRate = dvw / viewSizeW.value;
        vhRate = dvh / viewSizeH.value;
      }
    }

    scale.value = roundNumber(Math.min(vwRate, vhRate));
  }

  const debounceCalcLayout = debounce({ delay: 100 }, calcLayout);

  const setupResizeObserver = () => {
    resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        if (entry.target === rootRef.value) {
          debounceCalcLayout();
        }
      }
    });

    resizeObserver.observe(rootRef.value);
  }

  onMounted(() => {
    calcLayout();
    setupResizeObserver();
    handleOrientationChange(mediaQuery);
    mediaQuery.addEventListener('change', handleOrientationChange);
  });

  onUnmounted(() => {
    resizeObserver?.disconnect();
    resizeObserver = null;
    mediaQuery.removeEventListener('change', handleOrientationChange);
  });

  watch([layout, orientation, isRotation], debounceCalcLayout);

  return { viewSizeW, viewSizeH, scale, layout }
}