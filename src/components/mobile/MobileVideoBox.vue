<script setup lang="ts">
import VideoPlayer from "@/components/VideoPlayer/index.vue";
import Logos from "@/components/widget/logos.vue";
import Toolbar from "@/components/widget/ToolBar.vue";
import CameraList from "@/components/widget/CameraList.vue";
import BetAmount from "@/components/widget/BetAmount.vue";
</script>

<template>
  <div class="video-box">
    <VideoPlayer />

    <div class="video-header">
      <Logos style="--logos-pb: 2px; --logos-gap: 6px" />
      <CameraList />
    </div>

    <div class="absolute inset-x-0 px-3 bottom-2.5 flex items-center z-1">
      <Toolbar class="w-full" :toolbar="['quality', 'setting', 'rate']" :size="28" :background="true" />
    </div>

    <div class="absolute bottom-0 inset-x-0 flex justify-center z-1">
      <BetAmount/>
    </div>
  </div>
</template>

<style scoped>
.video-box {
  position: relative;
  display: block;
  width: 100%;
  aspect-ratio: 16/9;
}

.video-header {
  position: absolute;
  top: 0;
  inset-inline: 0;
  padding: 1px 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 20px;
  background-color: rgba(0, 0, 0, 0.2);
  z-index: 1;
}
</style>
