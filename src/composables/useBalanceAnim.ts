import { animate, JSAnimation } from "animejs";
import { storeToRefs } from "pinia";
import { useUserStore } from "@/stores";
import { formatBalance } from "@/utils/utils";
import { onUnmounted } from "vue";

export const useBalanceAnim = () => {
  const { currencySymbol } = storeToRefs(useUserStore());

  let anim:JSAnimation | null = null;

  const run = (balanceEl: HTMLElement | null, oldAmount: number, nweAmount: number) => {
    if (!balanceEl) return;

    if (oldAmount === nweAmount) {
      balanceEl.textContent = `${currencySymbol.value} ${formatBalance(nweAmount)}`;
      return;
    }

    const obj = { balance: oldAmount };

    cleanAnim();

    anim = animate(obj, {
      balance: nweAmount,
      duration: 600,
      easing: 'out(2.5)',
      onRender: () => {
        balanceEl.textContent = `${currencySymbol.value} ${formatBalance(obj.balance)}`;
      },
      onComplete: (self) => {
        self.pause();
      },
    });
  }

  const cleanAnim = () => {
    if (anim) {
      anim.pause();
      anim = null;
    }
  }

  onUnmounted(() => {
    cleanAnim();
  });

  return { run };
}