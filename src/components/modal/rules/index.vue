<template>
  <GModal v-model:show="showModal" :title="$t('rules')">
    <GLoading :show="showLoading">
      <div v-if="gameRules" class="rules-content">
        <div v-html="gameRules"></div>
      </div>
      <GEmpty v-else class="h-50" />
    </GLoading>
  </GModal>
</template>

<script setup lang="ts">
import {onMounted, ref} from "vue";
import {storeToRefs} from "pinia";
import {useMainStore} from "@/stores";
import {useSocketFetch} from "@/composables/useSocketFetch";

import GModal from "@/components/ui/GModal/index.vue";
import GLoading from "@/components/ui/GLoading/index.vue";
import GEmpty from "@/components/ui/GEmpty/index.vue"

const { lang } = storeToRefs(useMainStore());

const gameRules = ref('');

const showModal = ref<boolean>(false);

const showLoading = ref<boolean>(false);

const show = () => {
  showModal.value = true;
}

const hide = () => {
  showModal.value = false;
}

const fetchData = async () => {
  showLoading.value = true;
  try {
    const { promise } = useSocketFetch(
      'get-game-rules-req',
      'get-game-rules-res',
      { language: lang.value }
    );
    const res = await promise;
    const { data, code } = res.data;
    if (code === 200) {
      gameRules.value = data?.notice || '';
    }
  } catch (e) {
    console.error('fetch data error', e);
  } finally {
    showLoading.value = false;
  }
}

onMounted(() => {
  show();
  fetchData();
});

defineExpose({ show, hide })
</script>

<style lang="scss" scoped>
:deep(.rules-content) {
  color: #fff;
  font-size: 12px;
  line-height: 18px;

  h1,h2,h3,h4,h5,h6 {
    font-weight: 700;
  }

  img {
    max-width: 100% !important;
    height: auto !important;
  }
}
</style>
