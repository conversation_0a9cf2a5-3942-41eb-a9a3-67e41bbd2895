import { onUnmounted } from "vue";

const TIME_OUT = 1000 * 60 * 3;

export const useNoResponse = (cb: () => void) => {
  let timer: any;

  const clean = () => {
    if (timer) {
      clearTimeout(timer);
      timer = undefined;
    }
  }

  const start = () => {
    clean();
    timer = setTimeout(cb, TIME_OUT);
  }

  const reset = () => {
    clean();
    start();
  }

  start();

  onUnmounted(() => clean());

  return { reset }
}