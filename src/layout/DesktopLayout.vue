<script setup lang="ts">
import {onMounted, useTemplateRef} from "vue";
import {storeToRefs} from "pinia";
import {useMainStore} from "@/stores";
import {getClientX, isMobileDevice, isTouchDevice} from "@/utils/utils.ts";
import {CameraType} from "@/constant/gameData.ts";

import VideoPlayer from "@/components/VideoPlayer/index.vue";
import DesktopHeaderNav from "@/components/desktop/DesktopHeaderNav.vue";
import DesktopBetPanel from "@/components/desktop/DesktopBetPanel.vue";
import DesktopHistory from "@/components/desktop/DesktopHistory.vue";
import Settlement from "@/components/widget/settlement/Index.vue";
import CameraTransition from "@/components/CameraTransition/index.vue";
import CountDown from "@/components/CountDown/index.vue";
import ToolBar from "@/components/widget/ToolBar.vue";

const { cameraInfo, isRotation } = storeToRefs(useMainStore());
const { toggleCameraType } = useMainStore();

const contentBoxRef = useTemplateRef('contentBoxRef');

let isMove = false;
let startX = 0;
let endX = 0;

const handleTouchStart = (e: TouchEvent | MouseEvent) => {
  if (e.target !== contentBoxRef.value) return;
  isMove = true;
  startX = getClientX(e);
  endX = getClientX(e);
}

const handleTouchMove = (e: TouchEvent | MouseEvent) => {
  if (!isMove) return;
  endX = getClientX(e);
}

const handleTouchEnd = () => {
  if (!isMove) return;
  const x = endX - startX;

  if (x > 100 && cameraInfo.value.type === CameraType.B) { // left
    toggleCameraType();
  }

  if (x < -100 && cameraInfo.value.type === CameraType.A) { // right
    toggleCameraType();
  }

  isMove = false;
}

onMounted(() => {
  if (isTouchDevice()) {
    contentBoxRef.value?.addEventListener('touchstart', handleTouchStart);
    contentBoxRef.value?.addEventListener('touchmove', handleTouchMove);
    contentBoxRef.value?.addEventListener('touchend', handleTouchEnd);
  } else {
    contentBoxRef.value?.addEventListener('mousedown', handleTouchStart);
    contentBoxRef.value?.addEventListener('mousemove', handleTouchMove);
    contentBoxRef.value?.addEventListener('mouseup', handleTouchEnd);
  }
});
</script>

<template>
  <div class="view-lobby-desktop">
    <div class="video-layer">
      <VideoPlayer />
    </div>

    <div class="ui-layer">
      <DesktopHeaderNav />

      <div ref="contentBoxRef" class="relative">
        <DesktopHistory />
        <CountDown />
        <div v-if="isMobileDevice() && isRotation" class="absolute bottom-5 right-1">
          <ToolBar :toolbar="['rate']" :background="true" :size="28" />
        </div>
      </div>

      <DesktopBetPanel class="mt-auto" />
    </div>

    <Settlement />
    <CameraTransition />
  </div>
</template>

<style scoped>
.view-lobby-desktop {
  position: relative;
  width: 100%;
  height: 100%;
}

.view-lobby-desktop .video-layer {
  position: absolute;
  width: 100%;
  height: 100%;
  inset: 0;
}

.view-lobby-desktop .ui-layer {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: minmax(0, 1fr);
  grid-template-rows: auto minmax(0, 1fr) auto;
}
</style>