<script setup lang="ts">
import {onMounted, onUnmounted, ref, watchEffect} from "vue";
import {storeToRefs} from "pinia";
import {useGameStore, useMainStore, usePersistStore, useUserStore} from "@/stores";
import {useWebSocket} from "@/composables/useWebSocket.ts";
import {useNoResponse} from "@/composables/useNoResponse";
import {useDisableZoom} from "@/composables/useDisableZoom.ts";
import {useVolume} from "@/composables/useVolume.ts";
import {SYSTEM_ERROR_CODE} from "@/constant";
import {SocketEventCode} from "@/constant/gameData.ts";
import {closeFullscreen, openFullscreen} from "@/utils/utils";
import emitter from "@/utils/emitter.ts";

import MaintainInfo from "@/components/MaintainInfo/index.vue";
import InitScene from "@/components/InitScene/index.vue";
import CanvasLayout from "@/layout/CanvasLayout.vue";
import MobileLayout from "@/layout/MobileLayout.vue";
import DesktopLayout from "@/layout/DesktopLayout.vue";

const { token } = storeToRefs(useUserStore());
const { setBalance, setCurrency, setCurrencySymbol, setUserId } = useUserStore();
const { isHorizontal, isSystemError } = storeToRefs(useMainStore());
const { setLangs, setSystemError } = useMainStore();
const { setPlayerBetTotal, setPlayerBetInfoMap, setBetAmountInfo } = usePersistStore();
const { isSocketOnline } = storeToRefs(useGameStore());
const {
  setIsSocketOnline,
  updateGameInfo,
  setVideoStreamList,
  setBetChipsList,
  setGamePlayType,
  setGameAwardAmount,
  setHistoryBallRate,
  setHistoryTrend,
} = useGameStore();

const loading = ref(true);

const { reset } = useNoResponse(() => {
  // 如果socket是连接状态并且没有系统维护或网络错误 触发掉线
  if (isSocketOnline.value && !isSystemError.value) {
    console.log('socket-no-response');
    emitter.emit('socket-no-response');
    setSystemError(SYSTEM_ERROR_CODE.NETWORK_ERROR);
  }
});

const setupSocketEvent = () => {
  if (!token.value) {
    setSystemError(SYSTEM_ERROR_CODE.SYSTEM_MAINTAIN);
    return;
  }
  const { connect, subscribe, send, disconnect, isConnected } = useWebSocket(
    `${import.meta.env.VITE_SOCKET_URL}/websocket/${token.value}`,
    () => {
      emitter.emit('get-game-config-req');
      emitter.emit('get-user-balance-req');
      emitter.emit('get-game-history-req', {});
    }
  );
  // 订阅socket事件 通过emitter分发出去
  subscribe(SocketEventCode.SOCKET_INIT, data => emitter.emit('socket-init', data));
  subscribe(SocketEventCode.WAIT, data => emitter.emit('game-wait', data));
  subscribe(SocketEventCode.BET, data => emitter.emit('game-bet', data));
  subscribe(SocketEventCode.BET_END, data => emitter.emit('game-bet-end', data));
  subscribe(SocketEventCode.GAME_START, data => emitter.emit('game-start', data));
  subscribe(SocketEventCode.GAME_START_COUNTDOWN, data => emitter.emit('game-start-countdown', data));
  subscribe(SocketEventCode.SETTLEMENT, data => emitter.emit('game-settlement', data));
  subscribe(SocketEventCode.NOTICE_ONE_PRIZE, data => emitter.emit('game-award', data));
  subscribe(SocketEventCode.BET_AMOUNT, data => emitter.emit('game-bet-amount', data));

  subscribe(SocketEventCode.GAME_PAUSED, data => emitter.emit('game-paused', data));
  subscribe(SocketEventCode.KICK_OUT, data => emitter.emit('kick-out', data));
  subscribe(SocketEventCode.USER_BET, data => emitter.emit('user-bet-res', data));
  subscribe(SocketEventCode.USER_REVOKE, data => emitter.emit('user-revoke-res', data));
  subscribe(SocketEventCode.GET_GAME_CONFIG, data => emitter.emit('get-game-config-res', data));
  subscribe(SocketEventCode.GET_USER_BALANCE, data => emitter.emit('get-user-balance-res', data));
  subscribe(SocketEventCode.GET_GAME_HISTORY, data => emitter.emit('get-game-history-res', data));
  subscribe(SocketEventCode.GET_GAME_HISTORY_BET, data => emitter.emit('get-game-history-bet-res', data));
  subscribe(SocketEventCode.GET_PLAYER_BET, data => emitter.emit('get-player-bet-res', data));
  subscribe(SocketEventCode.GET_GAME_RULES, data => emitter.emit('get-game-rules-res', data));

  // 游戏配置
  const handleGetGameConfigReq = () => send(SocketEventCode.GET_GAME_CONFIG);
  const handleGetGameConfigRes = (res: SocketData) => {
    const { code, data } = res.data || {};
    if (code === 200) {
      setLangs(data.languageList);
      setVideoStreamList(data.playUrl);
      setBetChipsList(data.gameBetConfig);
      setGamePlayType(data.typeList);
    }
  }
  emitter.on('get-game-config-req', handleGetGameConfigReq);
  emitter.on('get-game-config-res', handleGetGameConfigRes);

  // 用户余额
  const handleGetUserBalanceReq = () => send(SocketEventCode.GET_USER_BALANCE);
  const handleGetUserBalanceRes = (res: SocketData) => {
    const { code, data } = res.data || {};
    if (code === 200) {
      setBalance(data?.balance);
      setCurrency(data?.currency);
      setCurrencySymbol(data?.icon);
      setUserId(data?.userId);
    }
  }
  emitter.on('get-user-balance-req', handleGetUserBalanceReq);
  emitter.on('get-user-balance-res', handleGetUserBalanceRes);

  // 获取当前玩家投注结果
  const handleGetPlayerBetReq = (res: { roundId: string; gameId: string }) => send(SocketEventCode.GET_PLAYER_BET, res);
  const handleGetPlayerBetRes = (res: SocketData) => {
    const { data, code } = res.data || {};
    if (code === 200) {
      setPlayerBetTotal(data?.totalBet ?? 0);
      setPlayerBetInfoMap(data?.recordLists ?? []);
    }
  }
  emitter.on('get-player-bet-req', handleGetPlayerBetReq);
  emitter.on("get-player-bet-res", handleGetPlayerBetRes);

  //获取游戏历史结果
  const handleGetGameHistoryReq = (data: EmitterCallbackData<any, string>) => {
    const msgId = send(SocketEventCode.GET_GAME_HISTORY) ?? '';
    data.callback?.(msgId);
  }
  const handleGetGameHistoryRes = (res: SocketData) => {
    const { data, code } = res?.data || {};
    if (code === 200) {
      setHistoryBallRate(data?.rateMap ?? {});
      setHistoryTrend(data?.historyResultVOList ?? []);
    }
  }
  emitter.on('get-game-history-req', handleGetGameHistoryReq);
  emitter.on('get-game-history-res', handleGetGameHistoryRes);

  // 获取游戏规则请求
  const handleGetGameRulesReq = (data: EmitterCallbackData<{ language: string }, string>) => {
    const msgId = send(SocketEventCode.GET_GAME_RULES, data.data) ?? '';
    data.callback?.(msgId);
  }
  emitter.on('get-game-rules-req', handleGetGameRulesReq);

  //获取游戏历史下注记录请求
  const handleGetGameHistoryBetReq = (data: EmitterCallbackData<any, string>) => {
    const msgId = send(SocketEventCode.GET_GAME_HISTORY_BET) ?? '';
    data.callback?.(msgId);
  }
  emitter.on('get-game-history-bet-req', handleGetGameHistoryBetReq);

  // 投注请求
  const handleUserBetReq = (data: EmitterCallbackData<any, string>) => {
    const msgId = send(SocketEventCode.USER_BET, data.data) ?? '';
    data.callback?.(msgId);
  }
  emitter.on('user-bet-req', handleUserBetReq);

  // 撤销请求
  const handleUserRevokeReq = (data: EmitterCallbackData<any, string>) => {
    const msgId = send(SocketEventCode.USER_REVOKE, data.data) ?? '';
    data.callback?.(msgId);
  }
  emitter.on('user-revoke-req', handleUserRevokeReq);

  // socket首次进入返回当前游戏状态
  const handleSocketInit = (res: SocketData) => {
    const { data } = res?.data || {};

    const status = data?.state?.toString();

    switch (status) {
      case SocketEventCode.GAME_PAUSED:
        emitter.emit('game-paused', { ...res, path: status });
        break;
      case SocketEventCode.WAIT:
        emitter.emit('game-wait', { ...res, path: status });
        break;
      case SocketEventCode.BET:
        emitter.emit('game-bet', { ...res, path: status });
        break;
      case SocketEventCode.BET_END:
        emitter.emit('game-bet-end', { ...res, path: status });
        break;
      case SocketEventCode.GAME_START:
        emitter.emit('game-start', { ...res, path: status });
        break;
      case SocketEventCode.GAME_START_COUNTDOWN:
        emitter.emit('game-start-countdown', { ...res, path: status });
        break;
      case SocketEventCode.SETTLEMENT:
        emitter.emit('game-settlement', { ...res, path: status });
        break;
    }

    emitter.emit('get-player-bet-req', {
      roundId: data?.roundId,
      gameId: data?.gameId,
    });
  }
  emitter.on('socket-init', handleSocketInit);

  // 等待游戏开始投注
  const handleGameWait = (res: SocketData) => {
    reset();
    setSystemError(SYSTEM_ERROR_CODE.NONE);
    updateGameInfo(res);
    setGameAwardAmount(0);
    setPlayerBetTotal(0);
    setPlayerBetInfoMap([]);
    setBetAmountInfo({});
    emitter.emit('play-sound', 'SOUND_6');
    emitter.emit('get-user-balance-req');
    emitter.emit('show-switch-scenes');
  }
  emitter.on('game-wait', handleGameWait);

  // 游戏开始下注
  const handleGameBet = (res: SocketData) => {
    reset();
    updateGameInfo(res);
  }
  emitter.on('game-bet', handleGameBet);

  // 游戏下注结束
  const handleGameBetEnd = (res: SocketData) => {
    reset();
    updateGameInfo(res);
    emitter.emit('show-switch-scenes');
  }
  emitter.on('game-bet-end', handleGameBetEnd);

  // 游戏开始
  const handleGameStart = (res: SocketData) => {
    reset();
    updateGameInfo(res);
  }
  emitter.on('game-start', handleGameStart);

  // 游戏开始倒计时
  const handleGameStartCountdown = () => {
    reset();
    // 延迟7s对准直播流倒计时画面
    setTimeout(() => emitter.emit('play-sound', 'SOUND_14'), 7000);
  }
  emitter.on('game-start-countdown', handleGameStartCountdown);

  // 游戏结算
  const handleGameSettlement = (res: SocketData) => {
    reset();
    updateGameInfo(res);
    emitter.emit('play-sound', 'SOUND_13');
  }
  emitter.on('game-settlement', handleGameSettlement);

  // 中奖推送
  const handleGameAward = (res: SocketData) => {
    reset();
    updateGameInfo(res);
  }
  emitter.on('game-award', handleGameAward);

  // 游戏维护
  const handleGamePaused = (res: SocketData) => {
    if (res.data?.code === 200) {
      setSystemError(SYSTEM_ERROR_CODE.SYSTEM_MAINTAIN);
    }
  }
  emitter.on('game-paused', handleGamePaused);

  // 被挤下线
  const handleKickOut = () => {
    console.log("kick-out............")
    disconnect();
  }
  emitter.on('kick-out', handleKickOut);

  // socket假死
  const handleSocketNoResponse = () => {
    disconnect();
  }
  emitter.on('socket-no-response', handleSocketNoResponse);

  connect();

  watchEffect(() => setIsSocketOnline(isConnected.value));

  onUnmounted(() => {
    emitter.off('get-game-config-req', handleGetGameConfigReq);
    emitter.off('get-game-config-res', handleGetGameConfigRes);
    emitter.off('get-user-balance-req', handleGetUserBalanceReq);
    emitter.off('get-user-balance-res', handleGetUserBalanceRes);
    emitter.off('get-player-bet-req', handleGetPlayerBetReq);
    emitter.off('get-player-bet-res', handleGetPlayerBetRes);
    emitter.off('get-game-history-req', handleGetGameHistoryReq);
    emitter.off('get-game-history-res', handleGetGameHistoryRes);
    emitter.off('get-game-rules-req', handleGetGameRulesReq);
    emitter.off('get-game-history-bet-req', handleGetGameHistoryBetReq);
    emitter.off('user-bet-req', handleUserBetReq);
    emitter.off('user-revoke-req', handleUserRevokeReq);
    emitter.off('socket-init', handleSocketInit);
    emitter.off('game-wait', handleGameWait);
    emitter.off('game-bet', handleGameBet);
    emitter.off('game-bet-end', handleGameBetEnd);
    emitter.off('game-start', handleGameStart);
    emitter.off('game-start-countdown', handleGameStartCountdown);
    emitter.off('game-settlement', handleGameSettlement);
    emitter.off('game-award', handleGameAward);
    emitter.off('game-paused', handleGamePaused);
    emitter.off('kick-out', handleKickOut);
    emitter.off('socket-no-response', handleSocketNoResponse);
  });
}

const setupOtherEvent = () => {
  const setNetworkError = () => setSystemError(SYSTEM_ERROR_CODE.NETWORK_ERROR);
  emitter.on('socket-reconnect-fail', setNetworkError);
  onUnmounted(() => {
    emitter.off('socket-reconnect-fail', setNetworkError);
  });
}

const setupVolume = () => {
  const { playSound } = useVolume();
  const handlePlaySound = (key: string) => playSound(key);
  emitter.on('play-sound', handlePlaySound);
  onUnmounted(() => {
    emitter.off('play-sound', handlePlaySound);
  });
}

const handleFinish = () => {
  loading.value = false;
}

useDisableZoom();

setupVolume();

setupSocketEvent();
setupOtherEvent();

const toggleFullscreen = () => {
  if (document.fullscreenElement) {
    closeFullscreen()
  } else {
    openFullscreen();
  }
}

onMounted(() => {
  // document.addEventListener('click', toggleFullscreen);
});

onUnmounted(() => {
  // document.removeEventListener('click', toggleFullscreen);
});
</script>

<template>
  <CanvasLayout>
    <div id="view-box">
      <MaintainInfo v-if="isSystemError" />

      <InitScene v-if="loading" @finish="handleFinish" />

      <Component v-else :is="isHorizontal ? DesktopLayout : MobileLayout" />
    </div>
  </CanvasLayout>
</template>

<style scoped>
#view-box {
  width: 100%;
  height: 100%;
  position: relative;
  overscroll-behavior: none;
  overflow: hidden;
}
</style>