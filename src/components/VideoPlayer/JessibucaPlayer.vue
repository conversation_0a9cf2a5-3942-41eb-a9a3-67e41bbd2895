<script setup lang="ts">
import {computed, onMounted, onUnmounted, watch} from "vue";
import {storeToRefs} from "pinia";
import {debounce, sleep} from "radash";
import {useGameStore, useMainStore} from "@/stores";
import {CameraType} from "@/constant/gameData.ts";
import emitter from "@/utils/emitter.ts";

const props = defineProps<{
  type: CameraType;
}>()

const { cameraInfo, isSystemError, isShowVideoLoading } = storeToRefs(useMainStore());
const { useStreamList, isSocketOnline } = storeToRefs(useGameStore());
const { toggleVideoLoading } = useMainStore();

let jessibuca: Jessibuca | null = null;

const isCurrPlayer = computed(() => {
  return props.type === cameraInfo.value.type
});

const isNormalStatus = computed(() => {
  return !isSystemError.value && isSocketOnline.value && isCurrPlayer.value
});

const playUrl = computed(() => {
  const { quality } = cameraInfo.value;
  const { a, b } = useStreamList.value?.[quality] || {};
  const line = props.type === CameraType.A ? a : b;
  return line?.url;
});

const setupJessibucaPlayer = async () => {
  await stopPlayer(true);

  const container = document.getElementById(`jessibuca-player-${props.type}`);

  if (!container) return console.error('not fount jessibuca player container');

  console.log("===============", jessibuca);

  jessibuca = new Jessibuca({
    container,
    // debug: true,
    videoBuffer: 0.2,
    isResize: false,
    // isFullResize: true,
    keepScreenOn: true,
    // hiddenAutoPause: true,
    controlAutoHide: true,
  });

  jessibuca.toggleControlBar(false);

  jessibuca.on("start", function () {
    console.log("play start")
    toggleVideoLoading(false)
  });

  jessibuca.on("timeout", function (error) {
    console.log('timeout:', error)
  });

  jessibuca.on("loadingTimeout", function () {
    console.log('loadingTimeout')
  });

  jessibuca.on("delayTimeout", function () {
    console.log('delayTimeout')
  });

  jessibuca.on("error", function (error: any) {
    console.log('error:', error)
    if (error === Jessibuca.ERROR.fetchError) {
      //
    }
  });
}

const stopPlayer = async (isDestroy?: boolean) => {
  if (!jessibuca) return;
  
  await jessibuca.pause();

  if (isDestroy) {
    jessibuca.destroy();
    jessibuca = null;
  }
}

const startPlayer = async (isStop?: boolean) => {
  if (document.visibilityState === 'hidden') return;
  if (!jessibuca) return;
  if (!playUrl.value) return;
  if (!isNormalStatus.value) return;

  toggleVideoLoading(true);

  isStop && await stopPlayer();

  await jessibuca.play(playUrl.value);
}

const handleVisible = () => {
  if (document.visibilityState === 'visible') {
    startPlayer();
  } else {
    stopPlayer();
  }
}

const handleReStart = () => startPlayer(true);

const handleSwitchStream = async () => {
  // if (isShowVideoLoading.value) return;
  toggleVideoLoading(true);
  await setupJessibucaPlayer();
  await sleep(100);
  await startPlayer();
}

const debounceSwitchStream = debounce({ delay: 300 }, handleSwitchStream);

onMounted(() => {
  emitter.on('show-switch-scenes', handleReStart);
  window.addEventListener('visibilitychange', handleVisible);

  // 播放流发生变化后，重新创建实例播放
  watch(playUrl, debounceSwitchStream, { immediate: true });

  watch(isNormalStatus, (status) => {
    if (status) {
      startPlayer();
    } else {
      stopPlayer();
    }
  });
});

onUnmounted(() => {
  emitter.off('show-switch-scenes', handleReStart);
  window.removeEventListener('visibilitychange', handleVisible);

  stopPlayer(true);
});
</script>

<template>
  <div class="size-full jussibuca-player" :id="`jessibuca-player-${type}`"></div>
</template>

<style lang="scss" scoped>
.jussibuca-player {
  :deep(canvas) {
    width: 100% !important;
    height: 100% !important;
    inset: 0 !important;
    transform: scale(1) !important;
  }
  :deep(.jessibuca-loading) {
    display: none !important;
    opacity: 0 !important;
  }
}
</style>
