import {computed, ref} from "vue";
// import {sleep, random} from "radash";

export const imageLoader = (src: string, cb: (data: any) => void) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = async () => {
      // await sleep(random(500, 2000))
      resolve(img);
      cb(img);
    };
    img.onerror = () => reject(new Error(`图片加载失败:${src}`));
    img.src = src;
  });
}

export const audioLoader = (src: string, cb: (data: any) => void) => {
  return new Promise((resolve, reject) => {
    let flag = false;
    const audio = new Audio();
    const then = () => {
      if (flag) return;
      flag = true;
      resolve(audio);
      cb(audio);
    }
    // Safari无法触发canplaythrough，使用setTimeout作为回退方案
    setTimeout(then, 3000);
    audio.addEventListener('canplaythrough', then);
    audio.addEventListener('error', () => reject(new Error(`音频加载失败:${src}`)));
    audio.src = src;
  });
}

export const useAssetLoader = () => {
  const total = ref(0);
  const current = ref(0);
  const finish = ref(false);
  const progress = computed(() => {
    if (current.value) return current.value / total.value;
    return 0;
  });

  const start = (manifest: Record<string, string[]>) => {
    const images = manifest?.images || [];
    const audios = manifest?.audios || [];
    total.value += images.length;
    total.value += audios.length;

    Promise.allSettled([
      ...images.map(image => imageLoader(image, () => current.value += 1)),
      ...audios.map(audio => audioLoader(audio, () => current.value += 1)),
    ]).catch((err) => {
      console.error(err);
    }).finally(() => {
      finish.value = true;
    });
  }

  return { total, current, finish, progress, start }
}