class AudioManager {
    private audios: Record<string, HTMLAudioElement> = {};
    private audioPools: Record<string, HTMLAudioElement[]> = {};
    private isInitialized: boolean = false;

    mainVolume = 1;
    soundVolume = 1;
    bgmVolume = 1;

    constructor({ mainVolume = 1, soundVolume = 1, bgmVolume = 1 }) {
        this.mainVolume = mainVolume;
        this.bgmVolume = bgmVolume;
        this.soundVolume = soundVolume;
    }

    // 预加载音频
    preload(sounds: SoundConfig[]): void {
        sounds.forEach(({ id, src, loop = false, poolSize = 1 }) => {
            if (loop) {
                // 背景音乐等单实例音频
                const audio = new Audio(src);
                audio.preload = 'auto';
                audio.loop = loop;
                audio.volume = this.bgmVolume;
                this.audios[id] = audio;
            } else {
                // 音效使用音频池
                this.audioPools[id] = [];
                for (let i = 0; i < poolSize; i++) {
                    const audio = new Audio(src);
                    audio.preload = 'auto';
                    audio.volume = this.soundVolume;
                    this.audioPools[id].push(audio);
                }
            }
        });
    }

    // 初始化音频上下文
    init(): void {
        if (!this.isInitialized) {
            // 播放无声音频以解锁音频上下文
            const silentAudio = new Audio('data:audio/wav;base64,UklGRiQAAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YQAAAAA=');
            silentAudio.play().catch(() => console.log('play audio fail'));
            this.isInitialized = true;
        }
    }

    // 播放音频
    play(id: string): void {
        if (!this.isInitialized) return;

        if (this.audios[id]) {
            // 播放单实例音频（如背景音乐）
            this.audios[id].currentTime = 0;
            this.audios[id].play().catch((e) => console.error(`play ${id} fail:`, e));
        } else if (this.audioPools[id]) {
            // 从音频池中获取可用实例
            const availableAudio = this.audioPools[id].find(audio => audio.paused || audio.ended) || this.audioPools[id][0];
            availableAudio.currentTime = 0;
            availableAudio.play().catch((e) => console.error(`play ${id} fail:`, e));
        }
    }

    // 暂停音频
    pause(id: string): void {
        const ad = this.audios[id];
        if (ad && (!ad.paused || !ad.ended)) {
            ad.pause();
        }
    }

    // 停止音频
    stop(id: string): void {
        if (this.audios[id]) {
            this.audios[id].pause();
            this.audios[id].currentTime = 0;
        }
    }

    private setSoundVolume = () => {
        Object.values(this.audioPools).forEach(audios => {
            audios.forEach(audio => {
                audio.volume = Math.max(0, Math.min(this.mainVolume, this.soundVolume))
            });
        });
    }

    private setBgmVolume = () => {
        Object.values(this.audios).forEach(audio => {
            audio.volume = Math.max(0, Math.min(this.mainVolume, this.bgmVolume));
        });
    }

    // 设置音量
    setVolume(type: 'main' | 'sound' | 'bgm', volume: number): void {
        if (type === 'main') {
            this.mainVolume = volume;
            this.setSoundVolume();
            this.setBgmVolume();
        }
        if (type === 'bgm') {
            this.bgmVolume = volume;
            this.setBgmVolume();
        }
        if (type === 'sound') {
            this.soundVolume = volume;
            this.setSoundVolume();
        }
    }

    // 暂停所有音频
    pauseAll(): void {
        Object.values(this.audios).forEach(audio => audio.pause());
        Object.values(this.audioPools).flat().forEach(audio => audio.pause());
    }
}

export default AudioManager;
