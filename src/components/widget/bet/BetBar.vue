<script setup lang="ts">
import {storeToRefs} from "pinia";
import {computed, nextTick, onMounted, onUnmounted, ref, useTemplateRef, watch, watchEffect} from "vue";
import {useI18n} from "vue-i18n";
import {sleep} from "radash";
import {useGameStore, useMainStore, useUserStore} from "@/stores";
import {GameStatus} from "@/constant/gameData.ts";
import {getCountDownText, formatAmount, amountProfitColor} from "@/utils/utils.ts";
import emitter from "@/utils/emitter.ts";
import {useBalanceAnim} from "@/composables/useBalanceAnim";
import {openModal} from "@/components/ui/GModal/index.ts";
import {openMessage} from "@/components/ui/GMessage/index.ts";
import {handleRevokeBet} from "./betHelper";

import RecordsModal from '@/components/modal/records/index.vue';
import BetChips from "./BetChips.vue";

const { balance } = storeToRefs(useUserStore());
const { toggleXHistoryShow, isHorizontal, isVertical } = useMainStore();
const { gameId, gameRoundId, gameStatus, gameAwardAmount, gameNextWaitEndTime, betChipsList } = storeToRefs(useGameStore());
const { t } = useI18n();

let timer: any;
let countdownText = ref('');

const balanceTextRef = useTemplateRef<HTMLElement>('balanceTextRef');

const { run } = useBalanceAnim();

const showChipsComp = computed(() => {
  if (gameStatus.value && betChipsList.value.length) {
    return [GameStatus.GAME_WAIT, GameStatus.GAME_BET].includes(gameStatus.value)
  }
  return false;
});

const showRecords = () => {
  openModal(RecordsModal, {});
};

const showHistory = () => {
  emitter.emit('play-sound', 'SOUND_1');
  toggleXHistoryShow();
};

const updateCountDownText = () => {
  if (timer) {
    clearTimeout(timer);
    timer = null;
  }

  const currTime = Date.now();

  if (gameNextWaitEndTime.value && gameNextWaitEndTime.value > currTime) {
    countdownText.value = getCountDownText(gameNextWaitEndTime.value - currTime)
    timer = setTimeout(updateCountDownText, 1000);
  } else {
    countdownText.value = "00:00:00";
  }
};

const handleRevokeBets = async () => {
  if (gameStatus.value !== GameStatus.GAME_BET) {
    openMessage({ message: t('can_not_revoke_tips') });
    return;
  }
  emitter.emit('play-sound', 'SOUND_1');
  // 撤销下注
  const promise = handleRevokeBet({ roundId: gameRoundId.value });
  try {
    const res = await promise;

    if (!res.data?.data?.flag) {
      return console.error('revoke bet fail', res);
    }

    emitter.emit('handle-revoke-bet');
    emitter.emit("get-user-balance-req");
    emitter.emit("get-player-bet-req", {
      gameId: gameId.value,
      roundId: gameRoundId.value,
    });
  } catch (e) {
    console.error('revoke bet error', e);
  }
};

const handleBetEnd = () => {
  emitter.emit("get-player-bet-req", {
    gameId: gameId.value,
    roundId: gameRoundId.value,
  });
  setTimeout(() => emitter.emit("get-user-balance-req"), 2000);
}

onMounted(() => {
  // 投注结束后拉取服务端投注信息修正本地数据
  emitter.on('game-bet-end', handleBetEnd);

  watchEffect(() => {
    if (gameStatus.value === GameStatus.GAME_SETTLEMENT && gameNextWaitEndTime.value) {
      updateCountDownText();
    }
  });

  watch(balance, async (newValue, oldValue) => {
    if (newValue === oldValue) return;
    await nextTick();

    if (gameStatus.value === GameStatus.GAME_SETTLEMENT) {
      await sleep(3400);
    }

    run(balanceTextRef.value, oldValue ?? 0, newValue ?? 0);
  }, {
    immediate: true
  });
});

onUnmounted(() => {
  emitter.off('game-bet-end', handleBetEnd);

  if (timer) {
    clearTimeout(timer)
    timer = null;
  }
});
</script>

<template>
  <section class="bet-bar-widget">
    <div class="btn-box left-box justify-start">
      <button v-if="isHorizontal" class="btn-item btn-dividing btn-dividing-right records-btn" @click="showRecords">
        <img src="/images/records-icon.png" alt="records" class="block size-5 object-contain" />
        <span class="btn-text">{{ $t('records') }}</span>
      </button>
      <button class="btn-item balance-btn">
        <span class="w-full break-all text-left text-white/80">{{ $t('balance') }}</span>
        <span ref="balanceTextRef" id="balance-text" :class="{ 'text-sm': isHorizontal, 'text-xs': isVertical }"></span>
      </button>
    </div>

    <div ref="actionBoxRef" class="action-box">
      <Transition name="switch-status">
        <BetChips v-if="showChipsComp" class="absolute" />

        <div v-else-if="gameStatus === GameStatus.GAME_SETTLEMENT" class="absolute w-full flex flex-col justify-center items-center">
          <div :class="{ 'hidden': isHorizontal }" class="font-bold flex flex-col items-center">
            <span class="text-[20px]" :style="{ color: amountProfitColor(gameAwardAmount) }">
              {{ formatAmount(gameAwardAmount, 2, false, true) }}
            </span>
            <span class="mt-1 text-[14px]">{{ $t('finally_make_a_profit') }}</span>
          </div>
          <div :class="{ 'hidden': isVertical }" class="text-[12px] font-bold">
            <span>{{ $t('finally_make_a_profit') }}:&nbsp;</span>
            <span :style="{ color: amountProfitColor(gameAwardAmount) }">
              {{ formatAmount(gameAwardAmount, 2, false, true) }}
            </span>
          </div>
          <div :class="{ 'hidden': isVertical }" class="text-[#20CFFF] mt-[12px]">{{ $t('countdown_to_the_next_round') }} {{ countdownText }}</div>
        </div>

        <div v-else class="absolute w-full flex flex-col items-center">
          <img src="/images/warn-icon.png" alt="warn" class="w-[18px]"/>
          <div :class="{ 'mt-[6px] text-[12px]': isHorizontal }" class="flex gap-0.5 flex-wrap justify-center">
            <span>{{ $t('the_game_is_in_progress') }},</span>
            <span>{{ $t('no_bets_available_now') }}!</span>
          </div>
        </div>
      </Transition>
    </div>

    <div class="btn-box right-box justify-end">
      <button class="btn-item revoke-btn" @click="handleRevokeBets">
        <img src="/images/revoke-icon.png" alt="revoke" class="block size-5 object-contain" />
        <span class="btn-text">{{ $t('revoke') }}</span>
      </button>
      <button v-if="isHorizontal" class="btn-item btn-dividing btn-dividing-left history-btn" @click="showHistory">
        <img src="/images/trend-history-icon.png" alt="trend history" class="block size-5 object-contain" />
        <span class="btn-text">{{ $t('trend_history') }}</span>
      </button>
    </div>
  </section>
</template>

<style lang="scss" scoped>
:where([data-layout="x"]) {
  .btn-box {
    width: 182px;

    &.left-box { clip-path: polygon(0 0, 134px 0, 100% 100%, 0% 100%); }
    &.right-box { clip-path: polygon(48px 0, 100% 0%, 100% 100%, 0% 100%); }

    .balance-btn { padding-right: 20px !important; }
    .revoke-btn { padding-left: 40px !important; }
    .records-btn { width: 70px; }
    .history-btn { width: 90px; }
  }

  .btn-box .btn-item.btn-dividing {
    position: relative;

    &.btn-dividing-left::before { left: 0; }
    &.btn-dividing-right::before { right: 0; }

    &:before {
      position: absolute;
      top: 0;
      bottom: 0;
      margin: auto;
      content: '';
      width: 1px;
      height: 34px;
      background-color: rgba(255, 255, 255, .3);
    }
  }

  .action-box {
    --chip-size: 30px;
  }
}

:where([data-layout="y"]) {
  .btn-box {
    width: 97px;

    &.left-box { clip-path: polygon(0 0, 51.84px 0, 100% 100%, 0% 100%); }
    &.right-box { clip-path: polygon(54px 0, 100% 0%, 100% 100%, 0% 100%); }

    .balance-btn { padding-right: 20px !important; }
    .revoke-btn { padding-left: 40px !important; }
  }

  .action-box {
    --chip-size: 32px;
  }
}

.bet-bar-widget {
  height: 54px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: white;
  background: linear-gradient(90deg, #860727 0%, rgba(134, 7, 39, 0) 40%, transparent 41%, transparent 59%, rgba(134, 7, 39, 0) 60%, #860727 100%) top/100% 2px no-repeat,
  linear-gradient(90deg, #860727 0%, rgba(134, 7, 39, 0) 45%, transparent 46%, transparent 54%, rgba(134, 7, 39, 0) 55%, #860727 100%) bottom/100% 2px no-repeat;
}

.btn-box {
  flex-shrink: 0;
  display: flex;
  height: 100%;
  background: linear-gradient(180deg, #BA002D 0%, #75001D 100%);
  box-shadow:
    -1px -1px 1px 0px rgba(0, 0, 0, 0.25) inset,
    1px 1px 1px 0px rgba(0, 0, 0, 0.25) inset;

  .balance-btn { flex: 1; }
  .revoke-btn { flex: 1; }

  .btn-item {
    cursor: pointer;
    outline: none;
    user-select: none;
    padding: 10px;
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .btn-text {
    width: 100%;
    color: rgba(255, 255, 255, 0.8);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  #balance-text {
    width: 100%;
    color: rgba(255, 255, 255, 0.9);
    text-align: left;
    font-weight: bold;
    line-height: 0.8;
  }
}

.action-box {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
  height: 100%;
  margin-inline: -14px;
  user-select: none;
}

.switch-status-enter-active,
.switch-status-leave-active {
  transition: all 0.3s ease-out;
}

.switch-status-enter-from {
  transform: rotateX(0);
  opacity: 0;
}

.switch-status-leave-to {
  transform: rotateX(180deg);
  opacity: 0;
}
</style>