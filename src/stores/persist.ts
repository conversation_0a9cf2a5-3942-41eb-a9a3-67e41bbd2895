import { ref } from "vue";
import { defineStore, storeToRefs } from "pinia";
import { useUserStore } from "./user";

export const usePersistStore = defineStore('persist', () => {
  // 当前期次 所有的下注金额信息
  const betAmountInfo = ref<Record<string, number>>({})
  const setBetAmountInfo = (data: Record<string, number>) => {
    betAmountInfo.value = data;
  }

  // 当前期次 当前玩家的下注总额
  const playerBetTotal = ref<number>(0)
  const setPlayerBetTotal = (data: number) => {
    playerBetTotal.value = data;
  }

  // 当前期次 当前玩家的下注金额信息
  const playerBetInfoMap = ref<Record<string, BetItemRes>>({})
  const setPlayerBetInfoMap = (data: BetItemRes[]) => {
    const playerBetMap:Record<string, BetItemRes> = {};
    if (data?.length) {
      data.forEach(betItem => {
        const { playTypeId, betLocation } = betItem;
        const location = `${playTypeId}-${betLocation}`;
        playerBetMap[location] = betItem;
      });
    }
    playerBetInfoMap.value = playerBetMap;
  }
  const updatePlayerBetInfo = (betInfo: Pick<BetItemRes, 'betAmount' | 'betLocation' | 'playTypeId'>) => {
    const { currency } = storeToRefs(useUserStore());
    const { playTypeId, betLocation, betAmount } = betInfo;
    const location = `${playTypeId}-${betLocation}`;
    const defaultBetInfo: BetItemRes = {
      betLocation: betLocation,
      playTypeId: playTypeId,
      currency: currency.value,
      betAmount: 0,
      count: 0,
    };
    const curBetInfo = playerBetInfoMap.value[location] ?? defaultBetInfo;
    curBetInfo.betAmount = (curBetInfo.betAmount * 1e3 + betAmount * 1e3) / 1e3;
    curBetInfo.count = curBetInfo.count + 1;
    playerBetInfoMap.value[location] = curBetInfo;
  }

  return {
    betAmountInfo, setBetAmountInfo,
    playerBetTotal, setPlayerBetTotal,
    playerBetInfoMap, setPlayerBetInfoMap, updatePlayerBetInfo,
  }
}, {
  persist: {
    key: 'persist-storage',
    storage: localStorage,
  }
});
