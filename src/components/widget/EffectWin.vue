<script setup lang="ts">
import { storeToRefs } from "pinia";
import { useGameStore, useMainStore } from "@/stores";
import { GameStatus } from "@/constant/gameData.ts";

const { isVertical, isHorizontal } = storeToRefs(useMainStore());
const { gameStatus } = storeToRefs(useGameStore());
</script>

<template>
  <div v-if="gameStatus === GameStatus.GAME_SETTLEMENT" class="absolute inset-0 m-auto w-full h-5 flex items-center justify-center">
    <div class="absolute inset-0" :class="{ 'scale-[1.5]': isVertical, 'scale-[1.4]': isHorizontal }">
      <img src="/images/win-anim.webp" class="block h-full w-auto mx-auto" alt="win bg" />
    </div>
    <img src="/images/win.png" class="block h-3 w-auto animate-scaleIn" alt="win" />
  </div>
</template>

<style scoped>
</style>