import { ref } from "vue";
import { defineStore } from "pinia";

export const useUserStore = defineStore('user', () => {
  const token = ref<string>();
  const setToken = (newToken: string) => {
    token.value = newToken;
  }

  const merchantId = ref<string>();
  const setMerchantId = (newMerchantId: string) => {
    merchantId.value = newMerchantId;
  }

  const userId = ref<string>();
  const setUserId = (newUserId: string) => {
    userId.value = newUserId;
  }

  const currency = ref<string>('PKR');
  const setCurrency = (newCurrency: string) => {
    currency.value = newCurrency;
  }

  const currencySymbol = ref<string>('Rs.');
  const setCurrencySymbol = (newCurrencySymbol: string) => {
    currencySymbol.value = newCurrencySymbol;
  }

  // 用户余额
  const balance = ref(0);
  const setBalance = (newBalance: number) => {
    balance.value = newBalance;
  }

  return {
    token, setToken,
    merchantId, setMerchantId,
    userId, setUserId,
    currency, setCurrency,
    currencySymbol, setCurrencySymbol,
    balance, setBalance,
  }
},{
  persist:{
    key:'token',
    pick:['token']
  }
});