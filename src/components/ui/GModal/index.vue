<script setup lang="ts">
import {computed, useTemplateRef} from "vue";
import type {ModalOptions} from "@/components/ui/GModal/types.ts";
import emitter from "@/utils/emitter.ts";

const props = defineProps<ModalOptions>()

const emits = defineEmits<{
  (e: 'close'): void
}>()

const show = defineModel<boolean>('show');

const modalRef = useTemplateRef('modalRef');

const modalWidth = computed(() => {
  if (props.width) {
    return typeof props.width === 'string' ? props.width : `${props.width}px`;
  }
  return '280px';
})

const handleClose = () => {
  emitter.emit('play-sound', 'SOUND_2')
  modalRef.value?.classList.add('hide');
  modalRef.value?.addEventListener('animationend', () => {
    emits('close');
    show.value = false;
  });
}
</script>

<template>
  <div v-if="show" ref="modalRef" class="g-modal fixed z-9999 inset-0 size-full bg-black/70 flex flex-col items-center justify-center">
    <div class="g-modal-inner flex flex-col bg-[#242328] max-h-9/10" :style="{ width: modalWidth }">
      <header class="modal-header relative flex items-center justify-center h-10 border-b border-[#302F32]">
        <h1 class="modal-header--title flex-1 px-8 text-center text-white text-sm font-bold">{{ title }}</h1>
        <div class="modal-header--close flex flex-col justify-center cursor-pointer absolute top-0 bottom-0 right-3 m-auto">
          <svg
              xmlns="http://www.w3.org/2000/svg"
              class="size-3 block transition-transform transform-content hover:rotate-90"
              viewBox="0 0 12 12"
              fill="none"
              @click="handleClose"
          >
            <rect width="2.29124" height="14.5749" transform="matrix(0.692704 -0.721222 0.704535 0.709669 0 1.65234)"
              fill="white" />
            <rect width="2.29124" height="14.5749" transform="matrix(-0.692704 -0.721222 -0.704535 0.709669 12 1.65234)"
              fill="white" />
          </svg>
        </div>
      </header>
      <div class="modal-body flex-1 px-3 py-4 overflow-hidden">
        <div class="size-full overflow-y-scroll scrollbar-hidden">
          <slot />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.g-modal {
  animation: modal-mask-show 0.3s ease-out forwards;
}

.g-modal-inner {
  animation: modal-inner-show 0.3s ease-out forwards;
}

.g-modal.hide {
  animation: modal-mask-hide 0.3s ease-out forwards;
}

.g-modal.hide .g-modal-inner {
  animation: modal-inner-hide 0.3s ease-out forwards;
}
</style>