/* color palette from <https://github.com/vuejs/theme> */

:root {
  --root-font-size: 16px;
  --bg-color: #030704;
}

html {
  font-size: var(--root-font-size, 16px);
}

html, body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  -webkit-user-select: none; /* 防止文本选择干扰 */
  touch-action: pan-x pan-y; /* 仅允许水平/垂直滚动，禁用缩放 */
}

body {
  overflow: hidden;
  overscroll-behavior: none;
  background-color: var(--bg-color);
  min-height: 100%;
  line-height: 1;
  font-size: var(--text-default);
  font-family: Inter, -apple-system, Roboto, 'Helvetica Neue', sans-serif;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  width: 100%;
  height: 100%;
}

.correct-container::after {
  content: "";
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  pointer-events: none;
  box-shadow:
          inset 0 0 1px 1px var(--bg-color),
          0 0 1px 1px var(--bg-color);
}

@keyframes modal-mask-show {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes modal-mask-hide {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes modal-inner-show {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes modal-inner-hide {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-20px);
  }
}