import axios from 'axios';
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';

// 定义接口返回数据的通用结构
export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message: string;
}

class Request {
  private instance: AxiosInstance;

  constructor(config: AxiosRequestConfig) {
    this.instance = axios.create(config);

    // 请求拦截器
    this.instance.interceptors.request.use(
      (config: InternalAxiosRequestConfig) => {
        // 在这里可以添加token等全局请求头
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.Authorization = token;
        }
        return config;
      },
      (error: any) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        // 在这里处理全局响应逻辑
        if (response.data.code !== 200) {
          return Promise.reject(response.data);
        }
        return response.data;
      },
      (error: any) => {
        // 在这里处理HTTP错误状态码
        if (error.response) {
          switch (error.response.status) {
            case 401:
              // 处理未授权
              break;
            case 403:
              // 处理禁止访问
              break;
            case 404:
              // 处理资源不存在
              break;
            case 500:
              // 处理服务器错误
              break;
          }
        }
        return Promise.reject(error);
      }
    );
  }

  // 封装请求方法
  public request<T = any>(config: AxiosRequestConfig): Promise<T> {
    return this.instance.request(config);
  }

  public get<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const c = Object.assign({ params: data ?? {} }, config ?? {});
    return this.instance.get(url, c);
  }

  public post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.post(url, data, config);
  }

  public put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.put(url, data, config);
  }

  public delete<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const c = Object.assign({ params: data ?? {} }, config ?? {});
    return this.instance.delete(url, c);
  }
}

// 创建全局实例
const request = new Request({
  baseURL: import.meta.env.VITE_API_URL, // 从环境变量获取
  timeout: 10000, // 10秒超时
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
});

export default request;