<script setup lang="ts">
import Logos from "@/components/widget/logos.vue";
import TimeInfo from "@/components/widget/TimeInfo.vue";
import Toolbar from "@/components/widget/ToolBar.vue";
import CameraList from "@/components/widget/CameraList.vue";
</script>

<template>
  <header class="grid grid-cols-[minmax(0,1fr)_auto_minmax(0,1fr)] grid-rows-1 h-8 items-center bg-black/20">
    <div class="h-full pl-6">
      <Logos style="--logos-pb: 7px; --logos-gap: 16px;" />
    </div>

    <TimeInfo />

    <div class="h-full pr-1 flex items-center justify-end gap-4">
      <Toolbar :toolbar="['quality', 'setting', 'rule']" />
      <CameraList />
    </div>
  </header>
</template>

<style scoped>

</style>