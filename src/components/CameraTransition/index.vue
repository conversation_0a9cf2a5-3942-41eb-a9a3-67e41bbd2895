<script lang="ts" setup>
import {computed, nextTick, onMounted, ref, watch} from 'vue'
import {createTimeline} from "animejs";
import {sleep} from "radash";
import {storeToRefs} from 'pinia';
import {useMainStore} from '@/stores';
import emitter from "@/utils/emitter.ts";

import Triangle from "@/components/CameraTransition/triangle.vue";

const { isHorizontal, isVertical } = storeToRefs(useMainStore());

const wrapperRef = ref();
const logoRef = ref();
const triangleList = ref([]);

const viewWidth = ref(0);
const viewHeight = ref(0);

const showing = ref(false);

const triangleSide = computed(() => {
  return Math.ceil(Math.max(viewWidth.value, viewHeight.value))
})

const show = () => {
  const openTl = createTimeline({
    onUpdate: function(self) {
      // 反向播放完毕
      if (self.progress === 1 && self.reversed) {
        self.cancel();
        showing.value = false;
      }
    },
    onComplete: async (self) => {
      emitter.emit('video-stream-start');
      await sleep(1000);
      self.reverse();
    },
  });

  openTl.set(logoRef.value, { opacity: 0, scale: 0 });
  openTl.set(triangleList.value, {
    translateX: Math.tan(Math.PI / 8) * triangleSide.value,
    translateY: -triangleSide.value,
  });

  openTl.add(logoRef.value, {
    opacity: 1,
    scale: 1.2,
    duration: 800,
    ease: 'inOutBack(5)'
  }, 0);
  openTl.add(triangleList.value, {
    translateX: 0,
    translateY: 0,
    duration: 800,
    ease: 'out(2)',
  }, 0);

  emitter.emit('play-sound', 'SOUND_8');
  emitter.emit('video-stream-stop');
}

const updateSize = () => {
  viewWidth.value = wrapperRef.value.clientWidth;
  viewHeight.value = wrapperRef.value.clientHeight;
  show();
}

watch(showing, (show) => {
  if (show) {
    nextTick(() => updateSize())
  }
})

emitter.on('show-switch-scenes', () => {
  showing.value = true;
});

onMounted(() => {
  // setTimeout(() => emitter.emit('show-switch-scenes'), 1000)
});
</script>

<template>
  <div>
    <div v-if="showing" ref="wrapperRef" class="absolute overflow-hidden inset-0 size-full z-9999 flex justify-center items-center">
      <svg
        v-for="item in 8"
        ref="triangleList"
        :key="item"
        :style="{
          height: `${triangleSide}px`,
          width: `${triangleSide}px`,
          top: -triangleSide + viewHeight / 2,
          left: -triangleSide + viewWidth / 2,
          rotate: `${45 * (item - 1)}deg`,
        }"
        class="absolute origin-bottom-right triangle-item"
      >
        <Triangle :triangle-side="triangleSide" />
      </svg>

      <img
        ref="logoRef"
        src="/images/logo-1.png"
        alt="logo"
        class="absolute inset-0 m-auto"
        :class="{ 'w-27': isVertical, 'w-24.5': isHorizontal }"
      />
    </div>
  </div>
</template>