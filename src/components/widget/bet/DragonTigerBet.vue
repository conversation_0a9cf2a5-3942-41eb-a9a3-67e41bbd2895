<script setup lang="ts">
import {onMounted, onUnmounted, ref, watchEffect} from "vue";
import {useI18n} from "vue-i18n";
import {storeToRefs} from "pinia";
import {useGameStore, useMainStore, usePersistStore, useUserStore} from "@/stores";
import {GamePlayType, GameStatus, SpecialBet} from "@/constant/gameData.ts"
import {openMessage} from "@/components/ui/GMessage/index.ts";
import {formatNumber} from "@/utils/utils.ts";
import emitter from "@/utils/emitter.ts";
import {
  handleBet,
  handleUpdateLocalBalance,
  handleUpdateLocalBetInfo,
  playBackBetAnim,
  playBetAnim,
  playToBalanceAnim
} from "./betHelper";

import EffectWin from "@/components/widget/EffectWin.vue"

const {
  gameId,
  gameRoundId,
  gamePlayType,
  gameStatus,
  gameWinBet,
} = storeToRefs(useGameStore());
const { isVertical, isHorizontal } = storeToRefs(useMainStore());
const { balance } = storeToRefs(useUserStore());
const { betAmountInfo, playerBetInfoMap } = storeToRefs(usePersistStore());
const { setPlayerBetTotal } = usePersistStore();

const { t } = useI18n();

const isDragonTiger = ref<string>();
const isBigger = ref<string>();
const isSingular = ref<string>();

const lists = ref<BetItemData[]>([
  {
    type: GamePlayType.DRAGON_TIGER,
    location: '1',
    amount: `${GamePlayType.DRAGON_TIGER}-1`,
    title: SpecialBet.DRAGON,
    odds: "1:0.95",
    image: "/images/bet-dragon.png",
    bets: 0,
    count: 0,
  },
  {
    type: GamePlayType.BIG_SMALL,
    location: '1',
    amount: `${GamePlayType.BIG_SMALL}-1`,
    title: SpecialBet.BIG,
    odds: "1:0.95",
    image: "",
    bets: 0,
    count: 0,
  },
  {
    type: GamePlayType.SINGULAR_EVEN,
    location: '1',
    amount: `${GamePlayType.SINGULAR_EVEN}-1`,
    title: SpecialBet.SINGULAR,
    odds: "1:0.95",
    image: "",
    bets: 0,
    count: 0,
  },
  {
    type: GamePlayType.SINGULAR_EVEN,
    location: '2',
    amount: `${GamePlayType.SINGULAR_EVEN}-2`,
    title: SpecialBet.EVEN,
    odds: "1:0.95",
    image: "",
    bets: 0,
    count: 0,
  },
  {
    type: GamePlayType.BIG_SMALL,
    location: '2',
    amount: `${GamePlayType.BIG_SMALL}-2`,
    title: SpecialBet.SMALL,
    odds: "1:0.95",
    image: "",
    bets: 0,
    count: 0,
  },
  {
    type: GamePlayType.DRAGON_TIGER,
    location: '2',
    amount: `${GamePlayType.DRAGON_TIGER}-2`,
    title: SpecialBet.TIGER,
    odds: "1:0.95",
    image: "/images/bet-tiger.png",
    bets: 0,
    count: 0,
  },
]);

const resetState = () => {
  isDragonTiger.value = "";
  isBigger.value = "";
  isSingular.value = "";
}

// 投放筹码
const takeBet = async (event: MouseEvent, index: any) => {
  //检查游戏阶段
  if (gameStatus.value !== GameStatus.GAME_BET) {
    return openMessage({message: t('no_bets_available_tips')});
  }

  const target = event.currentTarget as HTMLElement;

  const chip = document.querySelector(".chip-item.active") as HTMLElement;
  const chipInner = chip?.querySelector(".chip-item--inner") as HTMLElement;
  const targetEl = target.querySelector('.bets-icon') as HTMLElement;

  if (!chip || !chipInner || !targetEl) return console.error('not fount chip element');

  //检查下注是否冲突
  if (isBetConflict(index)) return;
  if (isBiggerConflict(index)) return;
  if (isSingularConflict(index)) return;

  //获取下注金额
  const amount = Number(chip.dataset.value ?? 0);

  if (balance.value < Number(amount)) {
    openMessage({ message: t('balance_not_enough_tis') });
    emitter.emit("user-bet-balance-fail");
    return;
  }

  emitter.emit('play-sound', 'SOUND_5');

  playBetAnim(chipInner, targetEl, 20);

  try {
    const promise = handleBet({
      betAmount: amount,
      gameId: gameId.value,
      roundId: gameRoundId.value,
      type: lists.value[index].type,
      location: lists.value[index].location,
    });

    const res = await promise;

    setPlayerBetTotal(res.data?.data?.total);  
    handleUpdateLocalBalance(amount);
    handleUpdateLocalBetInfo({
      betAmount: amount,
      betLocation: lists.value[index].location,
      playTypeId: lists.value[index].type,
    });
  } catch (e) {
    console.error('bet error', e);
  }
};

// 撤销投注
const backBets = async () => {
  resetState();

  const chipInner: HTMLElement | null = document.querySelector(".chip-item.active .chip-item--inner");
  const betList: HTMLImageElement[] = Array.from(document.querySelectorAll('.dragon-tiger-bets'));
  
  if (!chipInner || !betList.length) return;

  playBackBetAnim(betList, chipInner);
};

// 播放中奖后余额更新动画
const updateBetBalance = () => {
  const chips: HTMLImageElement[] = Array.from(document.querySelectorAll('.dragon-tiger-bet-item.win .dragon-tiger-bets'));
  const balance = document.querySelector('#balance-text') as HTMLElement;
  if (chips.length && balance) {
    playToBalanceAnim(chips, balance);
  }
};

//判断龙虎下注是否冲突
const isBetConflict = (index: number) => {
  const { type, location } = lists.value[index] ?? {};

  if (type !== GamePlayType.DRAGON_TIGER) {
    return false;
  }

  if (isDragonTiger.value) {
    const isConflict = isDragonTiger.value != location;
    if (isConflict) {
      openMessage({message: t('no_bets_available_type_tips')});
    }
    return isConflict;
  } else {
    isDragonTiger.value = location;
    return false;
  }
};

//判断大小下注是否冲突
const isBiggerConflict = (index: number) => {
  const { type, location } = lists.value[index] ?? {};

  if (type !== GamePlayType.BIG_SMALL) {
    return false;
  }

  if (isBigger.value) {
    const isConflict = isBigger.value != location;
    if (isConflict) {
      openMessage({message: t('no_bets_available_size_tips')});
    }
    return isConflict;
  } else {
    isBigger.value = location;
    return false;
  }
};

//判断单双下注是否冲突
const isSingularConflict = (index: number) => {
  const { type, location } = lists.value[index] ?? {};

  if (type !== GamePlayType.SINGULAR_EVEN) {
    return false;
  }

  if (isSingular.value) {
    const isConflict = isSingular.value != location;
    if (isConflict) {
      openMessage({message: t('no_bets_available_odd_even_tips')});
    }
    return isConflict;
  } else {
    isSingular.value = location;
    return false;
  }
};

// 判断指定投注区域是否获胜，用以展示获胜效果
const getShowWin = (item: any) => {
  if (item.title === SpecialBet.DRAGON) return gameWinBet.value.type2Loc === 1;
  if (item.title === SpecialBet.TIGER) return gameWinBet.value.type2Loc === 2;
  if (item.title === SpecialBet.BIG) return gameWinBet.value.type3Loc === 1;
  if (item.title === SpecialBet.SMALL) return gameWinBet.value.type3Loc === 2;
  if (item.title === SpecialBet.SINGULAR) return gameWinBet.value.type4Loc === 1;
  if (item.title === SpecialBet.EVEN) return gameWinBet.value.type4Loc === 2;
};

watchEffect(() => {
  resetState();
  lists.value.forEach(item => {
    const betItem = playerBetInfoMap.value[item.amount];
    if (!betItem) {
      item.bets = 0;
      item.count = 0;
      return;
    }
    item.bets = betItem.betAmount;
    item.count = betItem.count;
    // 更新 龙虎/大小/单双 状态
    if (betItem.playTypeId == GamePlayType.DRAGON_TIGER) {
      isDragonTiger.value = betItem.betLocation;
    }
    if (betItem.playTypeId == GamePlayType.BIG_SMALL) {
      isBigger.value = betItem.betLocation;
    }
    if (betItem.playTypeId == GamePlayType.SINGULAR_EVEN) {
      isSingular.value = betItem.betLocation;
    }
  });
});


watchEffect(() => {
  if (gamePlayType.value?.length) {
    lists.value.forEach(item => {
      const playType = gamePlayType.value?.find(i => i.playTypeId === item.type);
      if (playType) {
        item.odds = `1:${playType.rate[0]}`;
      }
    });
  }
});

watchEffect(() => {
  if (gameStatus.value === GameStatus.GAME_SETTLEMENT) {
    setTimeout(updateBetBalance, 1600)
  }
});

onMounted(() => {
  emitter.on("handle-revoke-bet", backBets);
});

onUnmounted(() => {
  emitter.off("handle-revoke-bet", backBets);
});
</script>

<template>
  <div
    class="dragon-tiger-bet-widget grid"
    :class="{
      'grid-cols-1 gap-2': isVertical,
      'grid-cols-2 gap-0.5': isHorizontal,
    }"
  >
    <div class="flex" :class="{ 'gap-0.5': isHorizontal, 'gap-2': isVertical }">
      <section
        v-for="(item, index) in lists.slice(0, 3)"
        :key="index"
        class="dragon-tiger-bet-item cursor-pointer overflow-hidden dragon flex-1"
        :class="{ win: getShowWin(item) }"
        @click="takeBet($event, index)"
      >
        <EffectWin v-if="getShowWin(item)"/>
        <div class="bet-item--top">
          <img v-show="item.image" :src="item.image" alt="dragon"/>
          <div>
            <div class="title">{{ item.title }}</div>
            <div class="odds">{{ item.odds }}</div>
          </div>
        </div>
        <div class="bet-item--bottom flex align-center justify-between">
          <span class="bets-text text-ellipsis overflow-hidden">
            <span class="text-[#EFE70C]">{{ formatNumber(item.bets) }}</span>/{{ formatNumber(betAmountInfo[item.amount] ?? 0) }}
          </span>
          <div class="bets-icon w-8 h-3.5 shrink-0">
            <template v-if="item.count > 0">
              <img :src="`/images/bets-${Math.min(item.count, 3)}.png`" :alt="`${index}-bet`" class="dragon-tiger-bets block h-3.5 ml-auto"/>
            </template>
          </div>
        </div>
      </section>
    </div>
    <div class="flex" :class="{ 'gap-0.5': isHorizontal, 'gap-2 flex-row-reverse': isVertical }">
      <section
        v-for="(item, index) in lists.slice(3)"
        :key="index"
        class="dragon-tiger-bet-item cursor-pointer overflow-hidden tiger flex-1"
        :class="{ win: getShowWin(item) }"
        @click="takeBet($event, index + 3)"
      >
        <EffectWin v-if="getShowWin(item)"/>
        <div class="bet-item--top">
          <img v-show="item.image" :src="item.image" alt="tiger"/>
          <div>
            <div class="title">{{ item.title }}</div>
            <div class="odds">{{ item.odds }}</div>
          </div>
        </div>
        <div class="bet-item--bottom flex align-center justify-between">
          <span class="bets-text text-ellipsis overflow-hidden">
            <span class="text-[#EFE70C]">{{ formatNumber(item.bets) }}</span>/{{ formatNumber(betAmountInfo[item.amount] ?? 0) }}
          </span>
          <div class="bets-icon w-8 h-3.5 shrink-0">
            <template v-if="item.count > 0">
              <img :src="`/images/bets-${Math.min(item.count, 3)}.png`" :alt="`${index}-bet`" class="dragon-tiger-bets block h-3.5 ml-auto"/>
            </template>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.dragon-tiger-bet-item.dragon {
  box-shadow: inset 0 0 1px 1px #001e80;
  background: url("@/assets/svg/MobileDragonBetGridBg.svg") right top no-repeat,
  linear-gradient(to right, #112c84 0%, #0026a7 7.5%, #00124e 100%) center/100% no-repeat;
}

.dragon-tiger-bet-item.dragon .bet-item--top .title {
  background-image: linear-gradient(180deg, #e4f9ff 25%, #b9ccff 100%);
}

.dragon-tiger-bet-item.tiger {
  box-shadow: inset 0 0 1px 1px #72001c;
  background: url("@/assets/svg/MobileTigerBetGridBg.svg") right top no-repeat,
  linear-gradient(to right, #a9001a, #4d000c) center/100% no-repeat;
}

.dragon-tiger-bet-item.tiger .bet-item--top .title {
  background-image: linear-gradient(180deg, #fff3e6 25%, #ffad76 100%);
}

.dragon-tiger-bet-item {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 6px;
  width: 100%;
  height: 66px;
}

.dragon-tiger-bet-item::after {
  opacity: 0;
  pointer-events: none;
  position: absolute;
  inset: 0;
  margin: auto;
  width: 100%;
  height: 100%;
  transition: opacity 0.6s ease;
  transform: scale(1.03);
  content: "";
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  background-image: url("/images/bet-light-border1.png");
}

.dragon-tiger-bet-item:hover::after {
  transition: none;
  opacity: 1;
}

.dragon-tiger-bet-item .bet-item--top {
  display: flex;
  gap: 4px;
  width: 100%;
}

.dragon-tiger-bet-item .bet-item--top img {
  flex-shrink: 0;
  display: block;
  width: 30px;
  height: 30px;
  border-radius: 50%;
}

.dragon-tiger-bet-item .bet-item--top .title {
  line-height: 1.2;
  font-size: 12px;
  font-weight: bolder;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.dragon-tiger-bet-item .bet-item--top .odds {
  margin-top: 4px;
  line-height: 1;
  color: white;
  font-size: 14px;
  font-weight: 700;
}

.dragon-tiger-bet-item .bet-item--bottom .bets-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  line-height: normal;
}
</style>
