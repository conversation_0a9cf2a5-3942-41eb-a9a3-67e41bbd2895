<script setup lang="ts">
import { computed } from "vue";
import { formatNumber } from '@/utils/utils';

const props = defineProps<{
  idx: number;
  amount: number;
  checked?: boolean;
}>();

const CHIPS_LIST = [
  { name: 'yellow', color: '#FEFF50', value: 1, key: 1 },
  { name: 'purple', color: '#EA65FF', value: 2, key: 2 },
  { name: 'green', color: '#5FFF5B', value: 3, key: 3, },
  { name: 'red', color: '#FF4761', value: 4, key: 4, },
  { name: 'pink', color: '#E728EA', value: 5, key: 5, },
  { name: 'grey', color: '#929292', value: 6, key: 6, },
  { name: 'cyan', color: '#28C0EA', value: 7, key: 7, },
  { name: 'light-green', color: '#28EA8F', value: 8, key: 8, },
  { name: 'dark-yellow', color: '#EAA628', value: 9, key: 9, },
];

const chipsInfo = computed(() => CHIPS_LIST[props.idx % CHIPS_LIST.length] ?? {});
</script>

<template>
  <div
      :data-value="amount"
      :style="`
        --chip-color: ${chipsInfo?.color};
        --chip-img: url('/images/chips-${chipsInfo?.name}.png');
      `"
      :class="{
        'chip-item': true,
        'active': checked,
      }"
  >
    <div class="chip-item--inner">
      <span class="chip-value">
        {{ formatNumber(amount) }}
      </span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.chip-item {
  --chip-img: none;
  --chip-color: none;

  flex-shrink: 0;
  width: 100%;
  display: flex;
  justify-content: center;

  &.active .chip-item--inner {
    transform: scale(1.25);
    filter: brightness(100%);
    box-shadow: 0 0 12px var(--chip-color);
    transition: transform 0.5s ease;
  }

  .chip-item--inner {
    display: block;
    margin: auto;
    width: var(--chip-size);
    height: var(--chip-size);
    border-radius: 50%;
    filter: brightness(70%);
    background-image: var(--chip-img);
    background-size: 100% 100%;
    background-position: center;
    cursor: pointer;
  }

  .chip-value {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -60%);
    font-weight: 700;
    text-shadow: -0.3px -0.3px 0 #000,
    0.3px -0.3px 0 #000,
    -0.3px 0.3px 0 #000,
    0.3px 0.3px 0 #000;
  }
}
</style>