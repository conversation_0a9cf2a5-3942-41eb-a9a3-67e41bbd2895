<script setup lang="ts">
import {onMounted, ref, watchEffect} from "vue";
import {storeToRefs} from "pinia";
import {useMainStore} from '@/stores';
import {useCanvasLayout} from "@/composables/useCanvasLayout";

const viewRef = ref();

const { orientation, isRotation } = storeToRefs(useMainStore());
const { setScale, setLayout } = useMainStore();

const { viewSizeW, viewSizeH, scale, layout } = useCanvasLayout(viewRef);

onMounted(() => {
  // 更新旋转值
  watchEffect(() => {
    if (isRotation.value) {
      // const rotate = orientation.value === 'landscape' ? '-90deg' : '90deg';
      viewRef.value.style.setProperty('--view-rotate', '90deg');
    } else {
      viewRef.value.style.setProperty('--view-rotate', '0deg');
    }
  });

  // 更新缩放值
  watchEffect(() => {
    setScale(scale.value)
    viewRef.value.style.setProperty('--view-scale', scale.value)
  });

  // 更新布局
  watchEffect(() => {
    setLayout(layout.value);
    viewRef.value.dataset.layout = layout.value;
  });
});
</script>

<template>
  <div ref="viewRef" class="view-wrapper">
    <div class="view-content correct-container">
      <slot />
    </div>
  </div>
</template>

<style>
.view-wrapper {
  --view-vw: calc(v-bind(viewSizeW) * 1px);
  --view-vh: calc(v-bind(viewSizeH) * 1px);
  --view-scale: 1;
  --view-rotate: 0;

  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.view-content {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: calc(var(--view-vh) / 2 * -1);
  margin-left: calc(var(--view-vw) / 2 * -1);
  width: var(--view-vw);
  height: var(--view-vh);
  transform: scale(var(--view-scale)) rotate(var(--view-rotate)) translateZ(0);
  transform-origin: center;
  background-color: var(--bg-color);
  user-select: none;
}
</style>
